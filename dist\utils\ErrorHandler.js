import chalk from 'chalk';
export class ErrorHandler {
    retryConfig;
    logLevel = 'info';
    constructor(retryConfig) {
        this.retryConfig = retryConfig;
    }
    setLogLevel(level) {
        this.logLevel = level;
    }
    async withRetry(operation, context, customRetryConfig) {
        const config = { ...this.retryConfig, ...customRetryConfig };
        let lastError;
        for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
            try {
                const result = await operation();
                if (attempt > 0) {
                    this.log('info', `Operation succeeded after ${attempt} retries`, context);
                }
                return result;
            }
            catch (error) {
                lastError = error;
                context.retryCount = attempt;
                if (attempt === config.maxRetries) {
                    this.log('error', `Operation failed after ${config.maxRetries} retries`, {
                        ...context,
                        error: lastError.message
                    });
                    break;
                }
                if (!this.isRetryableError(lastError, config)) {
                    this.log('error', 'Non-retryable error encountered', {
                        ...context,
                        error: lastError.message
                    });
                    break;
                }
                const delay = this.calculateDelay(attempt, config);
                this.log('warn', `Attempt ${attempt + 1} failed, retrying in ${delay}ms`, {
                    ...context,
                    error: lastError.message
                });
                await this.sleep(delay);
            }
        }
        throw lastError;
    }
    isRetryableError(error, config) {
        const errorMessage = error.message.toLowerCase();
        const errorCode = error.code;
        // Check for specific error codes
        if (errorCode && config.retryableErrors.includes(errorCode)) {
            return true;
        }
        // Check for error messages
        return config.retryableErrors.some(retryableError => errorMessage.includes(retryableError.toLowerCase()));
    }
    calculateDelay(attempt, config) {
        const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
        const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add jitter
        return Math.min(jitteredDelay, config.maxDelay);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    handleError(error, context) {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            context,
            timestamp: new Date().toISOString()
        };
        this.log('error', 'Error occurred', errorInfo);
        // Display user-friendly error message
        this.displayUserError(error, context);
    }
    displayUserError(error, context) {
        console.error(chalk.red('\n❌ Error occurred:'));
        console.error(chalk.red(`   ${error.message}`));
        if (context.operation) {
            console.error(chalk.gray(`   Operation: ${context.operation}`));
        }
        if (context.command) {
            console.error(chalk.gray(`   Command: ${context.command}`));
        }
        if (context.provider) {
            console.error(chalk.gray(`   Provider: ${context.provider}`));
        }
        if (context.model) {
            console.error(chalk.gray(`   Model: ${context.model}`));
        }
        // Provide helpful suggestions based on error type
        this.provideSuggestions(error, context);
    }
    provideSuggestions(error, context) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('api key') || errorMessage.includes('unauthorized')) {
            console.error(chalk.yellow('\n💡 Suggestion: Check your API key configuration'));
            console.error(chalk.gray('   Run: arien config set-api-key <your-api-key>'));
        }
        else if (errorMessage.includes('rate limit')) {
            console.error(chalk.yellow('\n💡 Suggestion: Rate limit exceeded'));
            console.error(chalk.gray('   Wait a moment and try again, or switch to a different model'));
        }
        else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
            console.error(chalk.yellow('\n💡 Suggestion: Check your internet connection'));
            console.error(chalk.gray('   Verify the provider\'s service status'));
        }
        else if (errorMessage.includes('model') || errorMessage.includes('not found')) {
            console.error(chalk.yellow('\n💡 Suggestion: Check if the model is available'));
            console.error(chalk.gray('   Run: arien models list'));
        }
        else if (errorMessage.includes('permission') || errorMessage.includes('access')) {
            console.error(chalk.yellow('\n💡 Suggestion: Check file/directory permissions'));
            console.error(chalk.gray('   Ensure you have the necessary access rights'));
        }
    }
    log(level, message, context) {
        const levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        if (levels[level] < levels[this.logLevel]) {
            return;
        }
        const timestamp = new Date().toISOString();
        const coloredLevel = this.colorizeLogLevel(level);
        console.error(`${chalk.gray(timestamp)} ${coloredLevel} ${message}`);
        if (context && this.logLevel === 'debug') {
            console.error(chalk.gray(JSON.stringify(context, null, 2)));
        }
    }
    colorizeLogLevel(level) {
        switch (level) {
            case 'debug':
                return chalk.blue('[DEBUG]');
            case 'info':
                return chalk.green('[INFO]');
            case 'warn':
                return chalk.yellow('[WARN]');
            case 'error':
                return chalk.red('[ERROR]');
            default:
                return `[${level.toUpperCase()}]`;
        }
    }
    // Specific error handlers
    handleNetworkError(error, context) {
        this.handleError(error, { ...context, operation: 'network_request' });
    }
    handleCommandError(error, command, context = {}) {
        this.handleError(error, {
            ...context,
            operation: 'command_execution',
            command,
            timestamp: new Date(),
            retryCount: 0
        });
    }
    handleProviderError(error, provider, model, context = {}) {
        this.handleError(error, {
            ...context,
            operation: 'llm_request',
            provider,
            model,
            timestamp: new Date(),
            retryCount: 0
        });
    }
    // Validation helpers
    validateApiKey(apiKey, provider) {
        if (!apiKey || apiKey.trim() === '') {
            throw new Error(`API key is required for ${provider}. Please set it using: arien config set-api-key <your-api-key>`);
        }
    }
    validateModel(model, availableModels) {
        if (!availableModels.includes(model)) {
            throw new Error(`Model "${model}" is not available. Available models: ${availableModels.join(', ')}`);
        }
    }
    validateCommand(command) {
        if (!command || command.trim() === '') {
            throw new Error('Command cannot be empty');
        }
        // Check for potentially dangerous commands
        const dangerousCommands = ['rm -rf /', 'format', 'del /f /s /q', 'shutdown', 'reboot'];
        const lowerCommand = command.toLowerCase();
        for (const dangerous of dangerousCommands) {
            if (lowerCommand.includes(dangerous)) {
                throw new Error(`Potentially dangerous command detected: "${dangerous}". Please review and confirm.`);
            }
        }
    }
}
//# sourceMappingURL=ErrorHandler.js.map