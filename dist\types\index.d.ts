export interface LLMProvider {
    name: string;
    models: string[];
    apiKey?: string;
    baseUrl?: string;
    isAvailable(): Promise<boolean>;
    generateResponse(messages: ChatMessage[], options?: GenerationOptions): Promise<LLMResponse>;
    streamResponse(messages: ChatMessage[], options?: GenerationOptions): AsyncGenerator<LLMStreamChunk>;
}
export interface ChatMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCalls?: ToolCall[];
    toolCallId?: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface LLMResponse {
    content: string;
    toolCalls?: ToolCall[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export interface LLMStreamChunk {
    content?: string;
    toolCalls?: ToolCall[];
    done: boolean;
}
export interface GenerationOptions {
    temperature?: number;
    maxTokens?: number;
    tools?: Tool[];
    toolChoice?: 'auto' | 'none' | {
        type: 'function';
        function: {
            name: string;
        };
    };
}
export interface Tool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    };
}
export interface ShellToolResult {
    success: boolean;
    stdout: string;
    stderr: string;
    exitCode: number;
    command: string;
    workingDirectory: string;
    executionTime: number;
    error?: string;
}
export interface TerminalSession {
    id: string;
    name: string;
    createdAt: Date;
    lastActivity: Date;
    messageCount: number;
    provider: string;
    model: string;
}
export interface TerminalConfig {
    provider: string;
    model: string;
    apiKey?: string;
    baseUrl?: string;
    temperature: number;
    maxTokens: number;
    autoApprove: boolean;
    theme: 'dark' | 'light';
    showTimestamps: boolean;
    saveHistory: boolean;
    debug?: boolean;
}
export interface CommandExecutionContext {
    command: string;
    workingDirectory: string;
    environment: Record<string, string>;
    timeout: number;
    requireApproval: boolean;
}
export interface RetryConfig {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    retryableErrors: string[];
}
export interface ErrorContext {
    operation: string;
    provider?: string;
    model?: string;
    command?: string;
    timestamp: Date;
    retryCount: number;
}
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: Record<string, any>;
}
//# sourceMappingURL=index.d.ts.map