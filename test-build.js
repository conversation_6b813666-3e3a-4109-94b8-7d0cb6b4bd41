#!/usr/bin/env node

/**
 * Simple test script to verify the build works
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Testing Arien AI CLI Build...\n');

// Check if dist directory exists
const distDir = join(__dirname, 'dist');
if (!existsSync(distDir)) {
  console.error('❌ dist directory not found. Run npm run build first.');
  process.exit(1);
}

// Check if main entry point exists
const mainFile = join(distDir, 'index.js');
if (!existsSync(mainFile)) {
  console.error('❌ Main entry point not found at dist/index.js');
  process.exit(1);
}

console.log('✅ dist directory exists');
console.log('✅ Main entry point exists');

// Check if key components exist
const components = [
  'terminal/TerminalInterface.js',
  'terminal/components/TerminalLayout.js',
  'terminal/components/ChatInputProcessor.js',
  'terminal/components/ChatOutputProcessor.js',
  'terminal/components/ToolCallsProcessor.js',
  'terminal/components/MessageHistory.js',
  'terminal/components/SlashCommands.js',
  'terminal/components/ThinkingSpinner.js',
  'terminal/components/OnboardingComponent.js',
  'providers/DeepseekProvider.js',
  'providers/OllamaProvider.js',
  'tools/ShellTool.js',
  'config/ConfigManager.js',
  'utils/ErrorHandler.js',
  'types/index.js'
];

let allComponentsExist = true;

for (const component of components) {
  const componentPath = join(distDir, component);
  if (existsSync(componentPath)) {
    console.log(`✅ ${component}`);
  } else {
    console.log(`❌ ${component} - MISSING`);
    allComponentsExist = false;
  }
}

if (!allComponentsExist) {
  console.error('\n❌ Some components are missing from the build.');
  process.exit(1);
}

console.log('\n🎉 All components built successfully!');
console.log('\nNext steps:');
console.log('1. Run: node scripts/install.js');
console.log('2. Or test locally: node dist/index.js --help');
console.log('3. Or install globally: npm install -g .');
