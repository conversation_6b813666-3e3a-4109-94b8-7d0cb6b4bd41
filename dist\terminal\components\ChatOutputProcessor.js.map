{"version": 3, "file": "ChatOutputProcessor.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/ChatOutputProcessor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,SAAS,MAAM,YAAY,CAAC;AAwBnC,MAAM,OAAO,mBAAoB,SAAQ,YAAY;IAC3C,aAAa,CAAgB;IAC7B,OAAO,CAAyB;IAChC,YAAY,GAAW,EAAE,CAAC;IAC1B,WAAW,GAAY,KAAK,CAAC;IAC7B,eAAe,GAAW,CAAC,CAAC;IAEpC,YACE,aAA4B,EAC5B,UAA2C,EAAE;QAE7C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG;YACb,eAAe,EAAE,IAAI;YACrB,gBAAgB,EAAE,IAAI;YACtB,gBAAgB,EAAE,IAAI;YACtB,eAAe,EAAE,KAAK;YACtB,cAAc,EAAE,EAAE;YAClB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAqB;QAC1C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAEzE,MAAM,SAAS,GAAoB;YACjC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU;YAC7C,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC/C,QAAQ,EAAE;gBACR,SAAS;gBACT,MAAM,EAAE,WAAW;gBACnB,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;gBAC/B,YAAY,EAAE,YAAY,IAAI,KAAK;gBACnC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,aAAa,EAAE,SAAS;aACzB;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;QAC3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,KAAqB;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,MAAM,EAAE,IAAI,CAAC,YAAY;gBACzB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;QACxD,MAAM,aAAa,GAAgB;YACjC,OAAO,EAAE,IAAI,CAAC,YAAY;YAC1B,KAAK,EAAE,SAAS,CAAC,oCAAoC;SACtD,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACtD,SAAS,CAAC,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;QAEjD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAqB;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC1B,CAAC;QAED,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,2BAA2B;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC;YAClD,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,gBAAgB;QAChB,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEpC,0DAA0D;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxD,SAAS,IAAI,OAAO,CAAC;QAErB,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QAED,qCAAqC;QACrC,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAC5B,cAAc,QAAQ,CAAC,KAAK,CAAC,WAAW,KAAK,QAAQ,CAAC,KAAK,CAAC,YAAY,MAAM,QAAQ,CAAC,KAAK,CAAC,gBAAgB,GAAG,CACjH,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe;QACtC,6CAA6C;QAC7C,OAAO,OAAO,CAAC,OAAO,CACpB,2BAA2B,EAC3B,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;YACxB,MAAM,IAAI,GAAG,QAAQ,IAAI,MAAM,CAAC;YAChC,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC;gBAC5C,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACxB,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC,CACF,CAAC,OAAO,CACP,YAAY,EACZ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,SAAqB;QAC3C,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEpD,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YACpC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACrD,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAE3C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBAC5C,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrE,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC;YAC5E,CAAC;YAED,SAAS,IAAI,IAAI,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAY,EAAE,OAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAE9E,MAAM,SAAS,GAAoB;YACjC,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC;YAClD,QAAQ,EAAE;gBACR,SAAS;gBACT,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,YAAY,EAAE,KAAK;aACpB;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QACxC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAY,EAAE,OAAgB;QAChD,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC;YAClD,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAEpC,IAAI,OAAO,EAAE,CAAC;YACZ,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;QAC5C,CAAC;QAED,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC;QAE3B,gCAAgC;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YACxD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAe,EAAE,OAAuC,MAAM;QACxF,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,MAAM,SAAS,GAAoB;YACjC,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO;YAChB,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC;YACzD,QAAQ,EAAE;gBACR,SAAS;gBACT,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,KAAK;aACpB;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QACjD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAe,EAAE,IAAoC;QAC/E,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC;YAClD,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,SAAS;gBACZ,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAClC,MAAM;YACR,KAAK,SAAS;gBACZ,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;QACV,CAAC;QAED,SAAS,IAAI,OAAO,CAAC;QACrB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,gBAAwB;QAC1C,OAAO,SAAS,CAAC,gBAAgB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAe;QACpC,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACjF,OAAO,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,kBAAkB;QAKvB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACtC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;CACF"}