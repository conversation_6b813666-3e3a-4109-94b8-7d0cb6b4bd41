export declare class TerminalInterface {
    private layout;
    private spinner;
    private slashCommands;
    private messageHistory;
    private onboarding;
    private inputProcessor;
    private outputProcessor;
    private toolProcessor;
    private configManager;
    private errorHandler;
    private shellTool;
    private providers;
    private currentProvider;
    private conversationHistory;
    private isProcessing;
    private currentSessionId;
    constructor();
    private initializeProviders;
    private setupEventHandlers;
    start(): Promise<void>;
    private runOnboarding;
    private validateProvider;
    private showWelcomeMessage;
    private handleUserInput;
    private processAIRequest;
    private prepareMessages;
    private streamAIResponse;
    private handleToolCalls;
    private isCriticalTool;
    private requestCommandApproval;
    private handleConfigChange;
    private navigateHistory;
    private handleAutoComplete;
    private getDefaultSystemPrompt;
    destroy(): void;
}
//# sourceMappingURL=TerminalInterface.d.ts.map