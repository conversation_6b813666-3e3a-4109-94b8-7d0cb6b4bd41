import { Tool, ShellToolResult, CommandExecutionContext } from '../types/index.js';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../utils/ErrorHandler.js';
export declare class ShellTool {
    private errorHandler;
    private currentDirectory;
    private environment;
    constructor(errorHandler: ErrorHandler);
    /**
     * Get the tool definition for LLM function calling
     * This provides comprehensive information about when and how to use the shell tool
     */
    getToolDefinition(): Tool;
    /**
     * Execute a shell command with comprehensive error handling and output processing
     */
    executeCommand(context: CommandExecutionContext): Promise<ShellToolResult>;
    /**
     * Execute multiple commands in sequence
     */
    executeSequential(commands: string[], workingDirectory?: string): Promise<ShellToolResult[]>;
    /**
     * Execute multiple commands in parallel
     */
    executeParallel(commands: string[], workingDirectory?: string): Promise<ShellToolResult[]>;
    private requestApproval;
    private displayCommandResult;
    private resolveDirectory;
    getCurrentDirectory(): string;
    setCurrentDirectory(directory: string): void;
    getEnvironment(): Record<string, string>;
    setEnvironmentVariable(key: string, value: string): void;
    /**
     * Check if a command requires user approval before execution
     */
    requiresApproval(command: string): boolean;
    /**
     * Get a list of safe commands that don't require approval
     */
    getSafeCommands(): string[];
}
//# sourceMappingURL=ShellTool.d.ts.map