import { TerminalConfig, TerminalSession, RetryConfig } from '../types/index.js';
export declare class ConfigManager {
    private config;
    private sessions;
    private configDir;
    constructor();
    private getDefaultConfig;
    getConfig(): TerminalConfig;
    updateConfig(updates: Partial<TerminalConfig>): void;
    getProvider(): string;
    setProvider(provider: string): void;
    getModel(): string;
    setModel(model: string): void;
    getApiKey(): string | undefined;
    setApiKey(apiKey: string): void;
    getBaseUrl(): string | undefined;
    setBaseUrl(baseUrl: string): void;
    getTemperature(): number;
    setTemperature(temperature: number): void;
    getMaxTokens(): number;
    setMaxTokens(maxTokens: number): void;
    isAutoApprove(): boolean;
    setAutoApprove(autoApprove: boolean): void;
    getTheme(): 'dark' | 'light';
    setTheme(theme: 'dark' | 'light'): void;
    shouldShowTimestamps(): boolean;
    setShowTimestamps(show: boolean): void;
    shouldSaveHistory(): boolean;
    setSaveHistory(save: boolean): void;
    getSessions(): Record<string, TerminalSession>;
    getSession(id: string): TerminalSession | undefined;
    saveSession(session: TerminalSession): void;
    deleteSession(id: string): void;
    clearSessions(): void;
    getRetryConfig(): RetryConfig;
    validateConfig(): {
        isValid: boolean;
        errors: string[];
    };
    reset(): void;
    exportConfig(): string;
    importConfig(configJson: string): void;
}
//# sourceMappingURL=ConfigManager.d.ts.map