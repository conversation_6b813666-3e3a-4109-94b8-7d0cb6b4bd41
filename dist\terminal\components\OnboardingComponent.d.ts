import { ConfigManager } from '../../config/ConfigManager.js';
export interface OnboardingResult {
    provider: string;
    model: string;
    apiKey?: string;
    baseUrl?: string;
    completed: boolean;
}
export declare class OnboardingComponent {
    private configManager;
    private errorHandler;
    constructor(configManager: ConfigManager);
    run(): Promise<OnboardingResult>;
    private showWelcomeBanner;
    private chooseProvider;
    private configureDeepseek;
    private configureOllama;
    private testConfiguration;
    private configurePreferences;
    private saveConfiguration;
    private showCompletionMessage;
    isOnboardingNeeded(): Promise<boolean>;
    runQuickSetup(): Promise<void>;
}
//# sourceMappingURL=OnboardingComponent.d.ts.map