export declare class ThinkingSpinner {
    private spinner;
    private isActive;
    private currentMessage;
    private readonly spinnerFrames;
    private readonly dotFrames;
    constructor();
    private setupCustomSpinner;
    start(message?: string): void;
    updateMessage(message: string): void;
    stop(): void;
    succeed(message?: string): void;
    fail(message?: string): void;
    warn(message?: string): void;
    info(message?: string): void;
    private startMessageRotation;
    isSpinning(): boolean;
    startThinking(): void;
    startExecuting(): void;
    startProcessing(): void;
    startConnecting(): void;
    startAnalyzing(): void;
    startGenerating(): void;
    startWithProgress(message: string, totalSteps: number): ProgressSpinner;
}
export declare class ProgressSpinner {
    private spinner;
    private currentStep;
    private totalSteps;
    private baseMessage;
    constructor(message: string, totalSteps: number);
    private getProgressText;
    private createProgressBar;
    updateProgress(step: number, message?: string): void;
    nextStep(message?: string): void;
    complete(message?: string): void;
    fail(message?: string): void;
    stop(): void;
}
export declare function createThemedSpinner(theme?: 'dark' | 'light'): ThinkingSpinner;
export declare function createCommandSpinner(command: string): ThinkingSpinner;
export declare function createAISpinner(provider: string, model: string): ThinkingSpinner;
//# sourceMappingURL=ThinkingSpinner.d.ts.map