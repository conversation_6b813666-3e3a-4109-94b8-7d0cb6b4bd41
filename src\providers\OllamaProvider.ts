import fetch from 'node-fetch';
import { 
  LLMProvider, 
  ChatMessage, 
  LLMResponse, 
  LLMStreamChunk, 
  GenerationOptions 
} from '../types/index.js';
import { <PERSON>rrorHandler } from '../utils/ErrorHandler.js';

export class OllamaProvider implements LLMProvider {
  name = 'ollama';
  models: string[] = [];
  baseUrl: string;
  private errorHandler: ErrorHandler;

  constructor(baseUrl = 'http://localhost:11434', errorHandler?: ErrorHandler) {
    this.baseUrl = baseUrl;
    this.errorHandler = errorHandler || new ErrorHandler({
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      retryableErrors: ['ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT']
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        // timeout: 5000 // Removed as it's not part of RequestInit
      });
      
      if (response.ok) {
        await this.loadModels();
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  async loadModels(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      if (response.ok) {
        const data = await response.json() as any;
        this.models = data.models?.map((model: any) => model.name) || [];
      }
    } catch (error) {
      // Keep empty models array if loading fails
    }
  }

  async generateResponse(messages: ChatMessage[], options?: GenerationOptions): Promise<LLMResponse> {
    if (this.models.length === 0) {
      await this.loadModels();
    }

    const model = this.getDefaultModel();
    const requestBody = {
      model,
      messages: this.formatMessages(messages),
      stream: false,
      options: {
        temperature: options?.temperature ?? 0.7,
        num_predict: options?.maxTokens ?? 4096
      },
      ...(options?.tools && {
        tools: this.formatTools(options.tools)
      })
    };

    return this.errorHandler.withRetry(
      async () => {
        const response = await fetch(`${this.baseUrl}/api/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`Ollama API error: ${response.status} - ${(errorData as any)?.error || response.statusText}`);
        }

        const data = await response.json() as any;
        
        return {
          content: data.message?.content || '',
          toolCalls: this.parseToolCalls(data.message?.content || ''),
          usage: {
            promptTokens: data.prompt_eval_count || 0,
            completionTokens: data.eval_count || 0,
            totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
          }
        };
      },
      {
        operation: 'ollama_generate_response',
        provider: this.name,
        model,
        timestamp: new Date(),
        retryCount: 0
      }
    );
  }

  async *streamResponse(messages: ChatMessage[], options?: GenerationOptions): AsyncGenerator<LLMStreamChunk> {
    if (this.models.length === 0) {
      await this.loadModels();
    }

    const model = this.getDefaultModel();
    const requestBody = {
      model,
      messages: this.formatMessages(messages),
      stream: true,
      options: {
        temperature: options?.temperature ?? 0.7,
        num_predict: options?.maxTokens ?? 4096
      },
      ...(options?.tools && {
        tools: this.formatTools(options.tools)
      })
    };

    const response = await this.errorHandler.withRetry(
      async () => {
        const res = await fetch(`${this.baseUrl}/api/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          throw new Error(`Ollama API error: ${res.status} - ${(errorData as any)?.error || res.statusText}`);
        }

        return res;
      },
      {
        operation: 'ollama_stream_response',
        provider: this.name,
        model,
        timestamp: new Date(),
        retryCount: 0
      }
    );

    if (!response.body) {
      throw new Error('No response body received from Ollama API');
    }

    const reader = (response.body as any)?.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          yield { done: true };
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed) continue;

          try {
            const parsed = JSON.parse(trimmed);
            
            if (parsed.done) {
              yield { done: true };
              return;
            }

            if (parsed.message?.content) {
              yield {
                content: parsed.message.content,
                toolCalls: this.parseToolCalls(parsed.message.content),
                done: false
              };
            }
          } catch (error) {
            // Skip invalid JSON lines
            continue;
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  private formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role === 'tool' ? 'user' : msg.role, // Ollama doesn't support tool role
      content: msg.role === 'tool' 
        ? `Tool result: ${msg.content}`
        : msg.content
    }));
  }

  private formatTools(tools: any[]): any[] {
    // Ollama's tool format might be different, adapt as needed
    return tools.map(tool => ({
      type: tool.type,
      function: {
        name: tool.function.name,
        description: tool.function.description,
        parameters: tool.function.parameters
      }
    }));
  }

  private parseToolCalls(content: string): any[] {
    // Simple tool call parsing for Ollama
    // This is a basic implementation - you might need to enhance based on your needs
    const toolCallRegex = /```json\s*(\{[^`]+\})\s*```/g;
    const toolCalls: any[] = [];
    let match: RegExpExecArray | null;

    while ((match = toolCallRegex.exec(content)) !== null) {
      try {
        const parsed = JSON.parse(match[1]);
        if (parsed.function && parsed.function.name) {
          toolCalls.push({
            id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'function',
            function: {
              name: parsed.function.name,
              arguments: JSON.stringify(parsed.function.arguments || {})
            }
          });
        }
      } catch (error) {
        // Skip invalid JSON
      }
    }

    return toolCalls;
  }

  private getDefaultModel(): string {
    if (this.models.length === 0) {
      return 'llama2'; // Fallback model
    }
    
    // Prefer models that are good for function calling
    const preferredModels = ['llama3', 'mistral', 'codellama', 'llama2'];
    
    for (const preferred of preferredModels) {
      const found = this.models.find(model => 
        model.toLowerCase().includes(preferred)
      );
      if (found) return found;
    }
    
    return this.models[0];
  }

  setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  async getModels(): Promise<string[]> {
    await this.loadModels();
    return this.models;
  }

  async pullModel(modelName: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/pull`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name: modelName })
    });

    if (!response.ok) {
      throw new Error(`Failed to pull model ${modelName}: ${response.statusText}`);
    }

    // Reload models after pulling
    await this.loadModels();
  }

  async deleteModel(modelName: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name: modelName })
    });

    if (!response.ok) {
      throw new Error(`Failed to delete model ${modelName}: ${response.statusText}`);
    }

    // Reload models after deletion
    await this.loadModels();
  }

  getSystemPrompt(): string {
    return `You are Arien AI, an intelligent CLI assistant that can execute shell commands to help users accomplish their tasks.

CORE CAPABILITIES:
- Execute shell commands safely and efficiently
- Analyze command outputs and provide insights
- Handle errors gracefully with retry logic
- Provide step-by-step guidance for complex tasks
- Maintain context across multiple command executions

IMPORTANT GUIDELINES:
1. Always explain what you're about to do before executing commands
2. Use function calls to execute shell commands for any system operations
3. Check command results and handle errors appropriately
4. Ask for confirmation before executing potentially destructive commands
5. Provide clear, actionable feedback based on command outputs
6. Break down complex tasks into smaller, manageable steps

FUNCTION CALLING:
When you need to execute shell commands, use JSON format like this:
\`\`\`json
{
  "function": {
    "name": "execute_shell_command",
    "arguments": {
      "command": "ls -la",
      "description": "List directory contents with details"
    }
  }
}
\`\`\`

Remember: Always prioritize user safety and system stability. Explain your actions clearly and handle errors gracefully.`;
  }
}
