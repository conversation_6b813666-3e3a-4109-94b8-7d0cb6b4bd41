import { ConfigManager } from '../../config/ConfigManager.js';
export interface SlashCommandResult {
    success: boolean;
    message: string;
    shouldExit?: boolean;
    shouldClear?: boolean;
    configChanged?: boolean;
}
export declare class SlashCommands {
    private configManager;
    private availableProviders;
    private availableModels;
    constructor(configManager: ConfigManager);
    executeCommand(input: string): Promise<SlashCommandResult>;
    private showHelp;
    private switchModel;
    private switchProvider;
    private manageSession;
    private showHistory;
    private clearConversation;
    private showConfig;
    private setConfig;
    private listModels;
    private listProviders;
    private listSessions;
    private exportConfig;
    private importConfig;
    private resetConfig;
    private exit;
    updateAvailableModels(provider: string, models: string[]): void;
    isSlashCommand(input: string): boolean;
    getAutoCompleteOptions(input: string): string[];
}
//# sourceMappingURL=SlashCommands.d.ts.map