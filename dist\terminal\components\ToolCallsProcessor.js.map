{"version": 3, "file": "ToolCallsProcessor.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/ToolCallsProcessor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAKtC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,QAAQ,MAAM,UAAU,CAAC;AAoBhC,MAAM,OAAO,kBAAmB,SAAQ,YAAY;IAC1C,aAAa,CAAgB;IAC7B,SAAS,CAAY;IACrB,YAAY,CAAe;IAC3B,OAAO,CAAuB;IAC9B,cAAc,GAAe,EAAE,CAAC;IAChC,gBAAgB,GAA8C,IAAI,GAAG,EAAE,CAAC;IAEhF,YACE,aAA4B,EAC5B,SAAoB,EACpB,YAA0B,EAC1B,UAAyC,EAAE;QAE3C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG;YACb,WAAW,EAAE,KAAK;YAClB,uBAAuB,EAAE,KAAK;YAC9B,kBAAkB,EAAE,CAAC;YACrB,gBAAgB,EAAE,KAAK;YACvB,2BAA2B,EAAE,IAAI;YACjC,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,SAAqB;QACjD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAE/C,MAAM,OAAO,GAA0B,EAAE,CAAC;QAE1C,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,SAAqB;QACnD,MAAM,OAAO,GAA0B,EAAE,CAAC;QAE1C,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAElD,0CAA0C;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;gBACxD,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,SAAqB;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,OAAO,GAA0B,EAAE,CAAC;QAE1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAClD,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE1B,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBACzC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;gBACzE,CAAC;qBAAM,CAAC;oBACN,MAAM,WAAW,GAAwB;wBACvC,QAAQ;wBACR,MAAM,EAAE,IAAI;wBACZ,OAAO,EAAE,KAAK;wBACd,aAAa,EAAE,CAAC;wBAChB,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,OAAO;qBACpC,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAkB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,IAAS,CAAC;YACd,IAAI,CAAC;gBACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,2BAA4B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,gCAAgC;YAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC/D,IAAI,QAAQ,GAAG,CAAC,gBAAgB,CAAC;YAEjC,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAClD,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,QAAQ;oBACR,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBACrC,KAAK,EAAE,+BAA+B;oBACtC,gBAAgB,EAAE,IAAI;oBACtB,QAAQ,EAAE,KAAK;iBAChB,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5E,OAAO;gBACL,QAAQ;gBACR,MAAM;gBACN,OAAO,EAAE,IAAI;gBACb,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,gBAAgB;gBAChB,QAAQ;aACT,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,QAAQ;gBACR,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,gBAAgB,EAAE,KAAK;gBACvB,QAAQ,EAAE,KAAK;aAChB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,YAAoB,EAAE,IAAS;QAC/D,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,uBAAuB;gBAC1B,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAE9C;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,YAAY,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,IAAS;QACzC,MAAM,OAAO,GAA4B;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,IAAK,UAAkB,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,IAAI,GAAG;YACvF,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB;YACtD,eAAe,EAAE,KAAK,CAAC,gCAAgC;SACxD,CAAC;QAEF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAkB,EAAE,IAAS;QACpD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAe;QACxC,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,WAAW;YAC5B,WAAW,EAAE,UAAU;YACvB,gBAAgB,EAAE,0BAA0B;YAC5C,MAAM,EAAE,oBAAoB;YAC5B,oBAAoB,EAAE,oBAAoB;YAC1C,eAAe,EAAE,YAAY;YAC7B,SAAS,EAAE,mBAAmB;YAC9B,iBAAiB,EAAE,uCAAuC;YAC1D,iBAAiB,EAAE,qCAAqC;YACxD,4BAA4B,EAAE,6BAA6B;YAC3D,+BAA+B,EAAE,qBAAqB;YACtD,UAAU,EAAE,mBAAmB;YAC/B,OAAO,EAAE,oBAAoB;YAC7B,QAAQ,EAAE,oBAAoB;YAC9B,cAAc,EAAE,mBAAmB;YACnC,QAAQ,EAAE,aAAa;YACvB,WAAW,EAAE,0BAA0B;YACvC,OAAO,EAAE,4BAA4B;YACrC,SAAS,EAAE,wBAAwB;SACpC,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAkB,EAAE,IAAS;QACzD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,wCAAwC,CAAC,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAEtC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC5C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YACzC;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,mCAAmC;gBAC5C,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAkB;QACvC,8DAA8D;QAC9D,MAAM,aAAa,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAChD,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,SAAqB;QAClD,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAA8B;QACjD,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAEjE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACjF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,aAAa,KAAK,CAAC,CAAC;YAEvD,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;YAE3F,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACvF,MAAM,IAAI,gBAAgB,cAAc,IAAI,CAAC;YAC/C,CAAC;YAED,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;YAC1D,CAAC;iBAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB,EAAE,MAAW;QACpD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAyB,CAAC,CAAC;YAE3D;gBACE,OAAO,cAAc,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAuB;QAC/C,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC;QAC/D,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,MAAM,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC;QACzE,CAAC;QAED,MAAM,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,QAAQ,mBAAmB,MAAM,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QAE1G,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB;QAQtB,oDAAoD;QACpD,mCAAmC;QACnC,OAAO;YACL,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAsC;QACzD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;CACF"}