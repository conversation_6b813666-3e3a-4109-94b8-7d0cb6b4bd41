import Conf from 'conf';
import { homedir } from 'os';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';
export class ConfigManager {
    config;
    sessions;
    configDir;
    constructor() {
        this.configDir = join(homedir(), '.arien-ai');
        // Ensure config directory exists
        if (!existsSync(this.configDir)) {
            mkdirSync(this.configDir, { recursive: true });
        }
        this.config = new Conf({
            configName: 'config',
            cwd: this.configDir,
            defaults: this.getDefaultConfig(),
            schema: {
                provider: { type: 'string' },
                model: { type: 'string' },
                apiKey: { type: 'string' },
                baseUrl: { type: 'string' },
                temperature: { type: 'number', minimum: 0, maximum: 2 },
                maxTokens: { type: 'number', minimum: 1 },
                autoApprove: { type: 'boolean' },
                theme: { type: 'string', enum: ['dark', 'light'] },
                showTimestamps: { type: 'boolean' },
                saveHistory: { type: 'boolean' }
            }
        });
        this.sessions = new Conf({
            configName: 'sessions',
            cwd: this.configDir,
            defaults: {}
        });
    }
    getDefaultConfig() {
        return {
            provider: 'deepseek',
            model: 'deepseek-chat',
            temperature: 0.7,
            maxTokens: 4096,
            autoApprove: false,
            theme: 'dark',
            showTimestamps: true,
            saveHistory: true
        };
    }
    getConfig() {
        return this.config.store;
    }
    updateConfig(updates) {
        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined) {
                this.config.set(key, value);
            }
        });
    }
    getProvider() {
        return this.config.get('provider');
    }
    setProvider(provider) {
        this.config.set('provider', provider);
    }
    getModel() {
        return this.config.get('model');
    }
    setModel(model) {
        this.config.set('model', model);
    }
    getApiKey() {
        return this.config.get('apiKey');
    }
    setApiKey(apiKey) {
        this.config.set('apiKey', apiKey);
    }
    getBaseUrl() {
        return this.config.get('baseUrl');
    }
    setBaseUrl(baseUrl) {
        this.config.set('baseUrl', baseUrl);
    }
    getTemperature() {
        return this.config.get('temperature');
    }
    setTemperature(temperature) {
        this.config.set('temperature', Math.max(0, Math.min(2, temperature)));
    }
    getMaxTokens() {
        return this.config.get('maxTokens');
    }
    setMaxTokens(maxTokens) {
        this.config.set('maxTokens', Math.max(1, maxTokens));
    }
    isAutoApprove() {
        return this.config.get('autoApprove');
    }
    setAutoApprove(autoApprove) {
        this.config.set('autoApprove', autoApprove);
    }
    getTheme() {
        return this.config.get('theme');
    }
    setTheme(theme) {
        this.config.set('theme', theme);
    }
    shouldShowTimestamps() {
        return this.config.get('showTimestamps');
    }
    setShowTimestamps(show) {
        this.config.set('showTimestamps', show);
    }
    shouldSaveHistory() {
        return this.config.get('saveHistory');
    }
    setSaveHistory(save) {
        this.config.set('saveHistory', save);
    }
    // Session management
    getSessions() {
        return this.sessions.store;
    }
    getSession(id) {
        return this.sessions.get(id);
    }
    saveSession(session) {
        this.sessions.set(session.id, session);
    }
    deleteSession(id) {
        this.sessions.delete(id);
    }
    clearSessions() {
        this.sessions.clear();
    }
    // Retry configuration
    getRetryConfig() {
        return {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffMultiplier: 2,
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'rate_limit_exceeded',
                'server_error',
                'service_unavailable'
            ]
        };
    }
    // Configuration validation
    validateConfig() {
        const errors = [];
        const config = this.getConfig();
        if (!config.provider) {
            errors.push('Provider is required');
        }
        if (!config.model) {
            errors.push('Model is required');
        }
        if (config.temperature < 0 || config.temperature > 2) {
            errors.push('Temperature must be between 0 and 2');
        }
        if (config.maxTokens < 1) {
            errors.push('Max tokens must be at least 1');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Reset to defaults
    reset() {
        this.config.clear();
        this.sessions.clear();
    }
    // Export/Import configuration
    exportConfig() {
        return JSON.stringify({
            config: this.config.store,
            sessions: this.sessions.store
        }, null, 2);
    }
    importConfig(configJson) {
        try {
            const data = JSON.parse(configJson);
            if (data.config) {
                Object.entries(data.config).forEach(([key, value]) => {
                    this.config.set(key, value);
                });
            }
            if (data.sessions) {
                Object.entries(data.sessions).forEach(([key, value]) => {
                    this.sessions.set(key, value);
                });
            }
        }
        catch (error) {
            throw new Error(`Failed to import configuration: ${error}`);
        }
    }
}
//# sourceMappingURL=ConfigManager.js.map