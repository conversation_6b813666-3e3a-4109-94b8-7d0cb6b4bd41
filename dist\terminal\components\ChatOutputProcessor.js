import { EventEmitter } from 'events';
import chalk from 'chalk';
import stripAnsi from 'strip-ansi';
export class ChatOutputProcessor extends EventEmitter {
    configManager;
    options;
    streamBuffer = '';
    isStreaming = false;
    streamStartTime = 0;
    constructor(configManager, options = {}) {
        super();
        this.configManager = configManager;
        this.options = {
            enableStreaming: true,
            enableFormatting: true,
            enableTimestamps: true,
            maxOutputLength: 10000,
            streamingDelay: 50,
            ...options
        };
    }
    /**
     * Process LLM response and format for display
     */
    processResponse(response) {
        const timestamp = new Date();
        const hasToolCalls = response.toolCalls && response.toolCalls.length > 0;
        const processed = {
            type: hasToolCalls ? 'tool_call' : 'response',
            content: response.content,
            formattedContent: this.formatResponse(response),
            metadata: {
                timestamp,
                source: 'assistant',
                length: response.content.length,
                hasToolCalls: hasToolCalls || false,
                toolCalls: response.toolCalls,
                executionTime: undefined
            }
        };
        this.emit('response_processed', processed);
        return processed;
    }
    /**
     * Process streaming response chunk
     */
    processStreamChunk(chunk) {
        if (!this.isStreaming) {
            this.startStreaming();
        }
        if (chunk.content) {
            this.streamBuffer += chunk.content;
            this.emit('stream_chunk', {
                content: chunk.content,
                buffer: this.streamBuffer,
                done: chunk.done
            });
        }
        if (chunk.toolCalls) {
            this.emit('tool_calls_received', chunk.toolCalls);
        }
        if (chunk.done) {
            this.finishStreaming();
        }
    }
    /**
     * Start streaming mode
     */
    startStreaming() {
        this.isStreaming = true;
        this.streamStartTime = Date.now();
        this.streamBuffer = '';
        this.emit('streaming_started');
    }
    /**
     * Finish streaming and process final result
     */
    finishStreaming() {
        const executionTime = Date.now() - this.streamStartTime;
        const finalResponse = {
            content: this.streamBuffer,
            usage: undefined // Will be populated by the provider
        };
        const processed = this.processResponse(finalResponse);
        processed.metadata.executionTime = executionTime;
        this.isStreaming = false;
        this.streamBuffer = '';
        this.emit('streaming_finished', processed);
    }
    /**
     * Format response for display
     */
    formatResponse(response) {
        if (!this.options.enableFormatting) {
            return response.content;
        }
        let formatted = '';
        // Add timestamp if enabled
        if (this.options.enableTimestamps) {
            const timestamp = new Date().toLocaleTimeString();
            formatted += chalk.gray(`[${timestamp}] `);
        }
        // Add AI prefix
        formatted += chalk.green('🤖 AI: ');
        // Format content with syntax highlighting for code blocks
        const content = this.formatCodeBlocks(response.content);
        formatted += content;
        // Add tool calls if present
        if (response.toolCalls && response.toolCalls.length > 0) {
            formatted += '\n\n' + this.formatToolCalls(response.toolCalls);
        }
        // Add usage information if available
        if (response.usage) {
            formatted += '\n' + chalk.gray(`📊 Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`);
        }
        return formatted;
    }
    /**
     * Format code blocks with syntax highlighting
     */
    formatCodeBlocks(content) {
        // Simple code block detection and formatting
        return content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            return chalk.blue(`\n┌─ ${lang.toUpperCase()} ─\n`) +
                chalk.white(code.trim()) +
                chalk.blue('\n└─────────────\n');
        }).replace(/`([^`]+)`/g, (match, code) => chalk.yellow(code));
    }
    /**
     * Format tool calls for display
     */
    formatToolCalls(toolCalls) {
        let formatted = chalk.cyan.bold('🔧 Tool Calls:\n');
        toolCalls.forEach((toolCall, index) => {
            formatted += chalk.cyan(`${index + 1}. ${toolCall.function.name}\n`);
            try {
                const args = JSON.parse(toolCall.function.arguments);
                formatted += chalk.gray('   Arguments:\n');
                Object.entries(args).forEach(([key, value]) => {
                    formatted += chalk.gray(`   • ${key}: ${JSON.stringify(value)}\n`);
                });
            }
            catch (error) {
                formatted += chalk.gray(`   Arguments: ${toolCall.function.arguments}\n`);
            }
            formatted += '\n';
        });
        return formatted;
    }
    /**
     * Process error message
     */
    processError(error, context) {
        const timestamp = new Date();
        const errorMessage = context ? `${context}: ${error.message}` : error.message;
        const processed = {
            type: 'error',
            content: errorMessage,
            formattedContent: this.formatError(error, context),
            metadata: {
                timestamp,
                source: 'system',
                length: errorMessage.length,
                hasToolCalls: false
            }
        };
        this.emit('error_processed', processed);
        return processed;
    }
    /**
     * Format error message
     */
    formatError(error, context) {
        let formatted = '';
        if (this.options.enableTimestamps) {
            const timestamp = new Date().toLocaleTimeString();
            formatted += chalk.gray(`[${timestamp}] `);
        }
        formatted += chalk.red('❌ Error: ');
        if (context) {
            formatted += chalk.yellow(`${context}: `);
        }
        formatted += error.message;
        // Add stack trace in debug mode
        if (this.configManager.getConfig().debug && error.stack) {
            formatted += '\n' + chalk.gray(error.stack);
        }
        return formatted;
    }
    /**
     * Process system message
     */
    processSystemMessage(message, type = 'info') {
        const timestamp = new Date();
        const processed = {
            type: 'system',
            content: message,
            formattedContent: this.formatSystemMessage(message, type),
            metadata: {
                timestamp,
                source: 'system',
                length: message.length,
                hasToolCalls: false
            }
        };
        this.emit('system_message_processed', processed);
        return processed;
    }
    /**
     * Format system message
     */
    formatSystemMessage(message, type) {
        let formatted = '';
        if (this.options.enableTimestamps) {
            const timestamp = new Date().toLocaleTimeString();
            formatted += chalk.gray(`[${timestamp}] `);
        }
        switch (type) {
            case 'info':
                formatted += chalk.blue('ℹ️  ');
                break;
            case 'warning':
                formatted += chalk.yellow('⚠️  ');
                break;
            case 'success':
                formatted += chalk.green('✅ ');
                break;
        }
        formatted += message;
        return formatted;
    }
    /**
     * Get plain text version of formatted content
     */
    getPlainText(formattedContent) {
        return stripAnsi(formattedContent);
    }
    /**
     * Truncate content if it exceeds maximum length
     */
    truncateContent(content) {
        if (content.length <= this.options.maxOutputLength) {
            return content;
        }
        const truncated = content.substring(0, this.options.maxOutputLength - 3) + '...';
        return truncated + chalk.gray('\n[Content truncated]');
    }
    /**
     * Get streaming status
     */
    getStreamingStatus() {
        return {
            isStreaming: this.isStreaming,
            bufferLength: this.streamBuffer.length,
            elapsedTime: this.isStreaming ? Date.now() - this.streamStartTime : 0
        };
    }
    /**
     * Clear stream buffer
     */
    clearStreamBuffer() {
        this.streamBuffer = '';
        this.isStreaming = false;
    }
}
//# sourceMappingURL=ChatOutputProcessor.js.map