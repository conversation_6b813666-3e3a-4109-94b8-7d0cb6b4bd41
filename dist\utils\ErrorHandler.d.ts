import { RetryConfig, ErrorContext, LogLevel } from '../types/index.js';
export declare class ErrorHandler {
    private retryConfig;
    private logLevel;
    constructor(retryConfig: RetryConfig);
    setLogLevel(level: LogLevel): void;
    withRetry<T>(operation: () => Promise<T>, context: ErrorContext, customRetryConfig?: Partial<RetryConfig>): Promise<T>;
    private isRetryableError;
    private calculateDelay;
    private sleep;
    handleError(error: Error, context: ErrorContext): void;
    private displayUserError;
    private provideSuggestions;
    private log;
    private colorizeLogLevel;
    handleNetworkError(error: Error, context: ErrorContext): void;
    handleCommandError(error: Error, command: string, context?: Partial<ErrorContext>): void;
    handleProviderError(error: Error, provider: string, model: string, context?: Partial<ErrorContext>): void;
    validateApiKey(apiKey: string | undefined, provider: string): void;
    validateModel(model: string, availableModels: string[]): void;
    validateCommand(command: string): void;
}
//# sourceMappingURL=ErrorHandler.d.ts.map