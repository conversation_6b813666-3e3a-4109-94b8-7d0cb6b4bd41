# 🤖 Arien AI CLI - Installation Guide

Modern and powerful CLI terminal system with LLM integration for executing shell commands intelligently.

## 🌍 Cross-Platform Support

- **Windows 11** (Native & WSL)
- **macOS** (Intel & Apple Silicon)
- **Linux** (Ubuntu, Debian, CentOS, Fedora, Arch, Alpine)

## 📋 Prerequisites

- **Node.js 20+** - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **git** (recommended)

## 🚀 Quick Installation

### Option 1: Automated Installation Scripts

#### Linux & macOS
```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# Run the installation script
chmod +x install.sh
./install.sh
```

#### Windows (PowerShell)
```powershell
# Clone the repository
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# Run the installation script
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\install.ps1
```

#### Windows WSL
```bash
# Use the Linux installation method
chmod +x install.sh
./install.sh
```

### Option 2: Manual Installation

```bash
# 1. Clone and navigate
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# 2. Install dependencies
npm install

# 3. Build the project
npm run build

# 4. Install globally
npm install -g .

# 5. Verify installation
arien --version
```

### Option 3: Development Installation

```bash
# For local development
npm install
npm run build

# Run locally
npm start
# or
node dist/index.js
```

## 🔧 Configuration

### First Time Setup

After installation, run the interactive setup:

```bash
arien
```

Or configure manually:

```bash
# Set your AI provider
arien config set-provider deepseek  # or ollama

# Set API key (for Deepseek)
arien config set-api-key your-api-key-here

# Set model
arien config set-model deepseek-chat

# View configuration
arien config show
```

### AI Providers

#### Deepseek (Cloud)
1. Get API key from [Deepseek Platform](https://platform.deepseek.com/)
2. Configure: `arien config set-api-key your-key`
3. Available models: `deepseek-chat`, `deepseek-reasoner`

#### Ollama (Local)
1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama2`
3. Configure: `arien config set-provider ollama`

## 🎯 Quick Start

```bash
# Start interactive mode
arien

# Show help
arien --help

# List providers
arien provider list

# List models
arien models list

# Run diagnostics
arien doctor
```

## 📖 Usage Examples

```bash
# Natural language commands
"List all files in the current directory"
"Check system memory usage"
"Install npm package express"
"Show git status and recent commits"
"Find all Python files in this project"

# Slash commands
/help              # Show help
/model llama2      # Switch model
/provider ollama   # Switch provider
/config            # Show configuration
/clear             # Clear conversation
/exit              # Exit application
```

## 🛠️ Troubleshooting

### Common Issues

#### Command not found
```bash
# Add npm global bin to PATH
echo 'export PATH="$(npm config get prefix)/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Or for zsh
echo 'export PATH="$(npm config get prefix)/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

#### Permission errors
```bash
# Use sudo for global installation
sudo npm install -g .

# Or configure npm to use different directory
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH="~/.npm-global/bin:$PATH"' >> ~/.bashrc
```

#### Windows PowerShell execution policy
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### WSL issues
- Ensure you're in Linux filesystem (`/home/<USER>
- Not in Windows filesystem (`/mnt/c/...`)

### System Diagnostics

Run the built-in diagnostics:

```bash
arien doctor
```

This will check:
- Node.js version
- Configuration validity
- Provider availability
- System compatibility

## 🔄 Updating

### Using Installation Scripts
```bash
# Linux/macOS
./install.sh
# Select option 2 (Update)

# Windows
.\install.ps1
# Select option 2 (Update)
```

### Manual Update
```bash
# Pull latest changes
git pull origin main

# Rebuild and reinstall
npm run build
npm install -g .
```

## 🗑️ Uninstallation

### Using Installation Scripts
```bash
# Linux/macOS
./install.sh
# Select option 3 (Uninstall)

# Windows
.\install.ps1
# Select option 3 (Uninstall)
```

### Manual Uninstall
```bash
# Remove global installation
npm uninstall -g arien-ai-cli

# Remove configuration (optional)
rm -rf ~/.arien-ai
```

## 🐛 Getting Help

- **Documentation**: [GitHub Repository](https://github.com/arien-ai/arien-ai-cli)
- **Issues**: [Report bugs](https://github.com/arien-ai/arien-ai-cli/issues)
- **Discussions**: [Community discussions](https://github.com/arien-ai/arien-ai-cli/discussions)

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**Happy coding with Arien AI! 🚀**
