#!/usr/bin/env node

/**
 * Arien AI CLI - Universal Cross-platform Installation Script
 *
 * Supports:
 * - Windows 11 (Native & WSL)
 * - macOS (Intel & Apple Silicon)
 * - Linux (Ubuntu, Debian, CentOS, Fedora, Arch, Alpine)
 *
 * Features:
 * - Global installation with automatic PATH setup
 * - Local development installation
 * - Update existing installations
 * - Complete uninstallation with config cleanup
 * - Automatic dependency detection and installation
 * - Shell integration (bash, zsh, fish, powershell)
 */

import { execSync, spawn } from 'child_process';
import { existsSync, readFileSync, writeFileSync, chmodSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { platform, homedir, arch, release } from 'os';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Enhanced colors and styling for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  // Foreground colors
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m',

  // Background colors
  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m'
};

function colorize(color, text) {
  return `${colors[color]}${text}${colors.reset}`;
}

function log(message, color = 'reset') {
  console.log(colorize(color, message));
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

class ArienInstaller {
  constructor() {
    this.platform = platform();
    this.arch = arch();
    this.release = release();
    this.isWSL = this.detectWSL();
    this.isDocker = this.detectDocker();
    this.distro = this.detectLinuxDistro();
    this.shell = this.detectShell();
    this.packageManager = this.detectPackageManager();
    this.nodeVersion = process.version;
    this.projectRoot = join(__dirname, '..');
    this.homeDir = homedir();
    this.configDir = join(this.homeDir, '.arien-ai');
    this.binDir = this.getBinDirectory();
  }

  detectWSL() {
    try {
      if (this.platform !== 'linux') return false;
      const release = readFileSync('/proc/version', 'utf8');
      return release.toLowerCase().includes('microsoft') ||
             release.toLowerCase().includes('wsl') ||
             existsSync('/mnt/c');
    } catch {
      return false;
    }
  }

  detectDocker() {
    try {
      return existsSync('/.dockerenv') ||
             (existsSync('/proc/1/cgroup') &&
              readFileSync('/proc/1/cgroup', 'utf8').includes('docker'));
    } catch {
      return false;
    }
  }

  detectLinuxDistro() {
    if (this.platform !== 'linux') return null;

    try {
      // Try /etc/os-release first (most modern distributions)
      if (existsSync('/etc/os-release')) {
        const osRelease = readFileSync('/etc/os-release', 'utf8');
        const idMatch = osRelease.match(/^ID=(.+)$/m);
        if (idMatch) {
          return idMatch[1].replace(/"/g, '').toLowerCase();
        }
      }

      // Fallback to specific release files
      const distroFiles = {
        '/etc/debian_version': 'debian',
        '/etc/redhat-release': 'rhel',
        '/etc/centos-release': 'centos',
        '/etc/fedora-release': 'fedora',
        '/etc/arch-release': 'arch',
        '/etc/alpine-release': 'alpine'
      };

      for (const [file, distro] of Object.entries(distroFiles)) {
        if (existsSync(file)) {
          return distro;
        }
      }
    } catch {
      // Ignore errors
    }

    return 'unknown';
  }

  detectShell() {
    const shell = process.env.SHELL || '';

    if (shell.includes('zsh')) return 'zsh';
    if (shell.includes('fish')) return 'fish';
    if (shell.includes('bash')) return 'bash';
    if (this.platform === 'win32') return 'powershell';

    return 'bash'; // Default fallback
  }

  getBinDirectory() {
    if (this.platform === 'win32') {
      return join(this.homeDir, 'AppData', 'Local', 'arien-ai', 'bin');
    }

    // Unix-like systems
    const possibleDirs = [
      join(this.homeDir, '.local', 'bin'),
      join(this.homeDir, 'bin'),
      '/usr/local/bin'
    ];

    // Return the first directory that exists or can be created
    for (const dir of possibleDirs) {
      try {
        if (existsSync(dir) || this.canCreateDirectory(dir)) {
          return dir;
        }
      } catch {
        continue;
      }
    }

    return possibleDirs[0]; // Fallback
  }

  canCreateDirectory(path) {
    try {
      mkdirSync(path, { recursive: true });
      return true;
    } catch {
      return false;
    }
  }

  detectPackageManager() {
    // Check for lock files first to determine preferred package manager
    const lockFiles = {
      'pnpm-lock.yaml': 'pnpm',
      'yarn.lock': 'yarn',
      'package-lock.json': 'npm'
    };

    for (const [lockFile, manager] of Object.entries(lockFiles)) {
      if (existsSync(join(this.projectRoot, lockFile))) {
        try {
          execSync(`${manager} --version`, { stdio: 'ignore' });
          return manager;
        } catch {
          continue;
        }
      }
    }

    // Check for available package managers
    const managers = ['pnpm', 'yarn', 'npm'];

    for (const manager of managers) {
      try {
        execSync(`${manager} --version`, { stdio: 'ignore' });
        return manager;
      } catch {
        continue;
      }
    }

    return 'npm'; // Fallback to npm
  }

  async run() {
    try {
      this.showBanner();
      this.checkSystem();
      await this.promptInstallationType();
    } catch (error) {
      logError(`Installation failed: ${error.message}`);
      process.exit(1);
    }
  }

  showBanner() {
    console.clear();
    log('╔════════════════════════════════════════════════════════════════════╗', 'cyan');
    log('║                    🤖 Arien AI CLI Universal Installer             ║', 'cyan');
    log('║                 Intelligent CLI Assistant with LLM                 ║', 'cyan');
    log('║                                                                    ║', 'cyan');
    log('║  🌍 Cross-Platform: Windows 11, WSL, macOS, Linux                 ║', 'gray');
    log('║  🧠 AI Providers: Deepseek, Ollama                                ║', 'gray');
    log('║  🔧 Features: Shell Commands, Real-time Streaming, Error Handling ║', 'gray');
    log('║  📦 Package Managers: npm, yarn, pnpm                             ║', 'gray');
    log('║  🐚 Shell Support: bash, zsh, fish, powershell                    ║', 'gray');
    log('╚════════════════════════════════════════════════════════════════════╝', 'cyan');
    log('');
  }

  checkSystem() {
    logStep('1', 'Analyzing system environment...');

    // Check Node.js version
    const nodeVersionNum = parseInt(this.nodeVersion.slice(1).split('.')[0]);
    if (nodeVersionNum < 20) {
      logError(`Node.js 20+ required. Current version: ${this.nodeVersion}`);
      logInfo('Please update Node.js: https://nodejs.org/');
      this.showNodeInstallInstructions();
      process.exit(1);
    }
    logSuccess(`Node.js ${this.nodeVersion} ✓`);

    // Platform information
    let platformInfo = this.platform;
    if (this.isWSL) platformInfo += ' (WSL)';
    if (this.isDocker) platformInfo += ' (Docker)';
    if (this.distro && this.distro !== 'unknown') platformInfo += ` - ${this.distro}`;

    logSuccess(`Platform: ${platformInfo} (${this.arch}) ✓`);

    // Shell information
    logSuccess(`Shell: ${this.shell} ✓`);

    // Package manager
    logSuccess(`Package manager: ${this.packageManager} ✓`);

    // Check permissions
    this.checkPermissions();

    // Check dependencies
    this.checkDependencies();

    log('');
  }

  checkPermissions() {
    try {
      // Test write permissions to bin directory
      if (!existsSync(this.binDir)) {
        mkdirSync(this.binDir, { recursive: true });
      }

      // Test write permissions to config directory
      if (!existsSync(this.configDir)) {
        mkdirSync(this.configDir, { recursive: true });
      }

      logSuccess('Permissions: Write access verified ✓');
    } catch (error) {
      logWarning(`Limited permissions detected. May require sudo for global installation.`);
    }
  }

  checkDependencies() {
    const requiredCommands = ['git'];
    const optionalCommands = ['curl', 'wget'];

    for (const cmd of requiredCommands) {
      try {
        execSync(`${cmd} --version`, { stdio: 'ignore' });
        logSuccess(`${cmd}: Available ✓`);
      } catch {
        logError(`${cmd}: Not found - required for installation`);
        this.showDependencyInstallInstructions(cmd);
      }
    }

    for (const cmd of optionalCommands) {
      try {
        execSync(`${cmd} --version`, { stdio: 'ignore' });
        logSuccess(`${cmd}: Available ✓`);
      } catch {
        logInfo(`${cmd}: Not found - optional but recommended`);
      }
    }
  }

  showNodeInstallInstructions() {
    log('');
    log('Node.js Installation Instructions:', 'yellow');
    log('');

    if (this.platform === 'win32') {
      log('Windows:', 'bright');
      log('  1. Download from: https://nodejs.org/');
      log('  2. Or use winget: winget install OpenJS.NodeJS');
      log('  3. Or use chocolatey: choco install nodejs');
    } else if (this.platform === 'darwin') {
      log('macOS:', 'bright');
      log('  1. Download from: https://nodejs.org/');
      log('  2. Or use Homebrew: brew install node');
      log('  3. Or use MacPorts: sudo port install nodejs20');
    } else {
      log('Linux:', 'bright');
      if (this.distro === 'ubuntu' || this.distro === 'debian') {
        log('  curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -');
        log('  sudo apt-get install -y nodejs');
      } else if (this.distro === 'centos' || this.distro === 'rhel' || this.distro === 'fedora') {
        log('  curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -');
        log('  sudo dnf install -y nodejs npm');
      } else if (this.distro === 'arch') {
        log('  sudo pacman -S nodejs npm');
      } else {
        log('  Visit: https://nodejs.org/ for distribution-specific instructions');
      }
    }
    log('');
  }

  showDependencyInstallInstructions(dependency) {
    log('');
    log(`${dependency} Installation Instructions:`, 'yellow');
    log('');

    if (dependency === 'git') {
      if (this.platform === 'win32') {
        log('Windows: Download from https://git-scm.com/');
      } else if (this.platform === 'darwin') {
        log('macOS: brew install git');
      } else {
        if (this.distro === 'ubuntu' || this.distro === 'debian') {
          log('Ubuntu/Debian: sudo apt-get install git');
        } else if (this.distro === 'centos' || this.distro === 'rhel' || this.distro === 'fedora') {
          log('CentOS/RHEL/Fedora: sudo dnf install git');
        } else if (this.distro === 'arch') {
          log('Arch: sudo pacman -S git');
        }
      }
    }
    log('');
  }

  async promptInstallationType() {
    log('Select installation type:', 'bright');
    log('1. 🌍 Global installation (recommended)');
    log('2. 📁 Local development installation');
    log('3. 🔄 Update existing installation');
    log('4. 🗑️  Uninstall Arien AI');
    log('5. 🔧 System diagnostics');
    log('6. ❌ Cancel');
    log('');

    const choice = await this.getInput('Enter your choice (1-6): ');

    switch (choice.trim()) {
      case '1':
        await this.installGlobal();
        break;
      case '2':
        await this.installLocal();
        break;
      case '3':
        await this.updateInstallation();
        break;
      case '4':
        await this.uninstall();
        break;
      case '5':
        await this.runDiagnostics();
        break;
      case '6':
        log('Installation cancelled.', 'yellow');
        process.exit(0);
        break;
      default:
        logError('Invalid choice. Please run the installer again.');
        process.exit(1);
    }
  }

  async runDiagnostics() {
    logStep('DIAG', 'Running system diagnostics...');

    log('System Information:', 'bright');
    log(`  OS: ${this.platform} ${this.release}`);
    log(`  Architecture: ${this.arch}`);
    log(`  WSL: ${this.isWSL ? 'Yes' : 'No'}`);
    log(`  Docker: ${this.isDocker ? 'Yes' : 'No'}`);
    if (this.distro) log(`  Distribution: ${this.distro}`);
    log(`  Shell: ${this.shell}`);
    log(`  Home Directory: ${this.homeDir}`);
    log(`  Config Directory: ${this.configDir}`);
    log(`  Bin Directory: ${this.binDir}`);
    log('');

    log('Node.js Environment:', 'bright');
    log(`  Version: ${this.nodeVersion}`);
    log(`  Package Manager: ${this.packageManager}`);
    try {
      const npmPrefix = execSync('npm config get prefix', { encoding: 'utf8' }).trim();
      log(`  NPM Prefix: ${npmPrefix}`);
    } catch {
      log('  NPM Prefix: Unable to determine');
    }
    log('');

    log('PATH Analysis:', 'bright');
    const pathDirs = process.env.PATH.split(this.platform === 'win32' ? ';' : ':');
    pathDirs.forEach(dir => {
      if (dir.includes('node') || dir.includes('npm') || dir.includes('.local')) {
        log(`  ${dir} ✓`, 'green');
      } else {
        log(`  ${dir}`, 'gray');
      }
    });
    log('');

    // Check for existing installation
    try {
      const version = execSync('arien --version', { encoding: 'utf8' }).trim();
      logSuccess(`Arien AI is installed: ${version}`);
    } catch {
      logInfo('Arien AI is not currently installed');
    }

    log('');
    const { continueInstall } = await this.getInput('Continue with installation? (y/N): ');
    if (continueInstall.toLowerCase() === 'y' || continueInstall.toLowerCase() === 'yes') {
      await this.promptInstallationType();
    }
  }

  async installGlobal() {
    logStep('2', 'Installing Arien AI globally...');

    try {
      // Install dependencies first
      logInfo('Installing dependencies...');
      this.execCommand(`${this.packageManager} install`);

      // Build the project
      logInfo('Building project...');
      this.execCommand(`${this.packageManager} run build`);

      // Install globally with proper error handling
      logInfo('Installing globally...');
      try {
        this.execCommand(`${this.packageManager} install -g .`);
      } catch (error) {
        if (error.message.includes('permission') || error.message.includes('EACCES')) {
          logWarning('Permission denied. Trying with elevated privileges...');
          if (this.platform !== 'win32') {
            this.execCommand(`sudo ${this.packageManager} install -g .`);
          } else {
            throw new Error('Please run as Administrator on Windows');
          }
        } else {
          throw error;
        }
      }

      // Setup shell integration
      await this.setupShellIntegration();

      // Verify installation
      try {
        this.execCommand('arien --version', { stdio: 'ignore' });
        logSuccess('Global installation completed successfully!');
      } catch {
        logWarning('Installation completed but `arien` command not found in PATH');
        this.showPathInstructions();
      }

      this.showPostInstallInstructions();

    } catch (error) {
      logError(`Global installation failed: ${error.message}`);
      this.showTroubleshootingTips();
      process.exit(1);
    }
  }

  async setupShellIntegration() {
    logInfo('Setting up shell integration...');

    const shellConfigs = {
      bash: ['.bashrc', '.bash_profile'],
      zsh: ['.zshrc'],
      fish: ['config/fish/config.fish'],
      powershell: ['Documents/PowerShell/Microsoft.PowerShell_profile.ps1']
    };

    const configs = shellConfigs[this.shell] || shellConfigs.bash;

    for (const configFile of configs) {
      const configPath = join(this.homeDir, configFile);

      try {
        // Ensure directory exists
        const configDir = dirname(configPath);
        if (!existsSync(configDir)) {
          mkdirSync(configDir, { recursive: true });
        }

        // Add PATH export if not already present
        let configContent = '';
        if (existsSync(configPath)) {
          configContent = readFileSync(configPath, 'utf8');
        }

        const pathExport = this.shell === 'fish'
          ? `set -gx PATH $PATH ${this.binDir}`
          : `export PATH="$PATH:${this.binDir}"`;

        if (!configContent.includes(this.binDir)) {
          const newContent = configContent + `\n# Added by Arien AI installer\n${pathExport}\n`;
          writeFileSync(configPath, newContent);
          logSuccess(`Updated ${configFile}`);
        }

        break; // Only update the first existing config file
      } catch (error) {
        logWarning(`Could not update ${configFile}: ${error.message}`);
      }
    }
  }

  async installLocal() {
    logStep('2', 'Setting up local development environment...');

    try {
      // Install dependencies
      logInfo('Installing dependencies...');
      this.execCommand(`${this.packageManager} install`);

      // Build project
      logInfo('Building project...');
      this.execCommand(`${this.packageManager} run build`);

      // Create local alias and scripts
      this.createLocalAlias();
      this.createDevelopmentScripts();

      logSuccess('Local installation completed successfully!');
      this.showLocalUsageInstructions();

    } catch (error) {
      logError(`Local installation failed: ${error.message}`);
      process.exit(1);
    }
  }

  async updateInstallation() {
    logStep('2', 'Updating Arien AI...');
    
    try {
      // Check if globally installed
      try {
        this.execCommand('arien --version', { stdio: 'ignore' });
        logInfo('Updating global installation...');
        
        // Uninstall old version
        this.execCommand(`${this.packageManager} uninstall -g arien-ai-cli`, { stdio: 'ignore' });
        
        // Install new version
        this.execCommand(`${this.packageManager} run build`);
        this.execCommand(`${this.packageManager} install -g .`);
        
        logSuccess('Global installation updated successfully!');
      } catch {
        logWarning('Global installation not found. Installing globally...');
        await this.installGlobal();
      }
      
    } catch (error) {
      logError(`Update failed: ${error.message}`);
      process.exit(1);
    }
  }

  async uninstall() {
    logStep('2', 'Uninstalling Arien AI...');
    
    const confirm = await this.getInput('Are you sure you want to uninstall Arien AI? (y/N): ');
    
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      log('Uninstallation cancelled.', 'yellow');
      return;
    }
    
    try {
      // Remove global installation
      try {
        this.execCommand(`${this.packageManager} uninstall -g arien-ai-cli`);
        logSuccess('Global installation removed');
      } catch {
        logWarning('Global installation not found');
      }
      
      // Remove configuration (optional)
      const removeConfig = await this.getInput('Remove configuration files? (y/N): ');
      
      if (removeConfig.toLowerCase() === 'y' || removeConfig.toLowerCase() === 'yes') {
        const configDir = join(homedir(), '.arien-ai');
        if (existsSync(configDir)) {
          this.execCommand(`rm -rf "${configDir}"`);
          logSuccess('Configuration files removed');
        }
      }
      
      logSuccess('Arien AI uninstalled successfully');
      
    } catch (error) {
      logError(`Uninstallation failed: ${error.message}`);
      process.exit(1);
    }
  }

  createLocalAlias() {
    const aliasScript = join(this.projectRoot, 'arien-local');
    const batchScript = join(this.projectRoot, 'arien-local.bat');

    // Create shell script for Unix-like systems
    const shellContent = `#!/bin/bash
# Arien AI Local Development Script
cd "${this.projectRoot}"
node "dist/index.js" "$@"`;

    // Create batch script for Windows
    const batchContent = `@echo off
REM Arien AI Local Development Script
cd /d "${this.projectRoot}"
node "dist\\index.js" %*`;

    if (this.platform === 'win32') {
      writeFileSync(batchScript, batchContent);
      logInfo(`Local batch script created: ${batchScript}`);
    } else {
      writeFileSync(aliasScript, shellContent);
      chmodSync(aliasScript, '755');
      logInfo(`Local shell script created: ${aliasScript}`);
    }
  }

  createDevelopmentScripts() {
    const scriptsDir = join(this.projectRoot, 'dev-scripts');

    if (!existsSync(scriptsDir)) {
      mkdirSync(scriptsDir, { recursive: true });
    }

    // Create development helper scripts
    const scripts = {
      'dev-start.sh': `#!/bin/bash
echo "🚀 Starting Arien AI in development mode..."
cd "${this.projectRoot}"
${this.packageManager} run dev`,

      'dev-build.sh': `#!/bin/bash
echo "🔨 Building Arien AI..."
cd "${this.projectRoot}"
${this.packageManager} run build`,

      'dev-test.sh': `#!/bin/bash
echo "🧪 Running tests..."
cd "${this.projectRoot}"
${this.packageManager} test`,

      'dev-lint.sh': `#!/bin/bash
echo "🔍 Linting code..."
cd "${this.projectRoot}"
${this.packageManager} run lint:fix`
    };

    for (const [filename, content] of Object.entries(scripts)) {
      const scriptPath = join(scriptsDir, filename);
      writeFileSync(scriptPath, content);

      if (this.platform !== 'win32') {
        chmodSync(scriptPath, '755');
      }
    }

    logInfo(`Development scripts created in: ${scriptsDir}`);
  }

  showPathInstructions() {
    log('');
    logWarning('The `arien` command was not found in your PATH.');
    log('You may need to add the global npm bin directory to your PATH:');
    log('');
    
    if (this.platform === 'win32' || this.isWSL) {
      log('For Windows/WSL:', 'bright');
      log('  npm config get prefix');
      log('  # Add the returned path to your PATH environment variable');
    } else if (this.platform === 'darwin') {
      log('For macOS:', 'bright');
      log('  echo \'export PATH="$(npm config get prefix)/bin:$PATH"\' >> ~/.zshrc');
      log('  source ~/.zshrc');
    } else {
      log('For Linux:', 'bright');
      log('  echo \'export PATH="$(npm config get prefix)/bin:$PATH"\' >> ~/.bashrc');
      log('  source ~/.bashrc');
    }
    log('');
  }

  showPostInstallInstructions() {
    log('');
    log('🎉 Installation Complete!', 'green');
    log('');
    log('Quick Start:', 'bright');
    log('  arien                    # Start interactive mode');
    log('  arien config show        # Show configuration');
    log('  arien provider list      # List available providers');
    log('  arien models list        # List available models');
    log('  arien --help             # Show all commands');
    log('');
    log('First Time Setup:', 'bright');
    log('  1. Set your API key: arien config set-api-key <your-key>');
    log('  2. Choose provider: arien config set-provider deepseek');
    log('  3. Start chatting: arien');
    log('');
    log('Documentation: https://github.com/arien-ai/arien-ai-cli', 'blue');
    log('');
  }

  showLocalUsageInstructions() {
    log('');
    log('🎉 Local Development Setup Complete!', 'green');
    log('');
    log('Usage:', 'bright');
    log(`  ${this.packageManager} run dev        # Start in development mode`);
    log(`  ${this.packageManager} run build      # Build the project`);
    log(`  ${this.packageManager} start          # Start built version`);
    log(`  ./arien-local                         # Use local alias`);
    log('');
  }

  showTroubleshootingTips() {
    log('');
    log('Troubleshooting Tips:', 'yellow');
    log('');
    log('1. Permission Issues:', 'bright');
    log('   • Try: sudo npm install -g .');
    log('   • Or configure npm to use a different directory');
    log('');
    log('2. Node.js Issues:', 'bright');
    log('   • Ensure Node.js 20+ is installed');
    log('   • Try: node --version');
    log('');
    log('3. Package Manager Issues:', 'bright');
    log('   • Try clearing cache: npm cache clean --force');
    log('   • Or use a different package manager: yarn/pnpm');
    log('');
    log('4. WSL Issues:', 'bright');
    log('   • Ensure you\'re in the Linux filesystem (/home/<USER>');
    log('   • Not in Windows filesystem (/mnt/c/...)');
    log('');
  }

  execCommand(command, options = {}) {
    logInfo(`Executing: ${command}`);
    return execSync(command, { 
      cwd: this.projectRoot,
      stdio: 'inherit',
      ...options 
    });
  }

  async getInput(prompt) {
    process.stdout.write(colorize('yellow', prompt));

    return new Promise((resolve) => {
      process.stdin.setRawMode(false);
      process.stdin.resume();
      process.stdin.once('data', (data) => {
        resolve(data.toString().trim());
      });
    });
  }
}

// Run the installer
if (import.meta.url === `file://${process.argv[1]}`) {
  const installer = new ArienInstaller();
  installer.run().catch(error => {
    logError(`Installer error: ${error.message}`);
    process.exit(1);
  });
}

export default ArienInstaller;
