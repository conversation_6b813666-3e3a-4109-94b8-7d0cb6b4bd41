{"version": 3, "file": "ShellTool.js", "sourceRoot": "", "sources": ["../../src/tools/ShellTool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,KAAK,MAAM,OAAO,CAAC;AAI1B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC,qBAAqB;AACrB,MAAM,QAAQ,GAAG,GAAG,EAAE;IACpB,IAAI,OAAO,UAAU,KAAK,WAAW,IAAK,UAAkB,CAAC,OAAO,EAAE,CAAC;QACrE,OAAQ,UAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC9C,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,OAAO,SAAS;IACZ,YAAY,CAAe;IAC3B,gBAAgB,CAAS;IACzB,WAAW,CAAyB;IAE5C,YAAY,YAA0B;QACpC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAI,UAAkB,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC;QACpE,IAAI,CAAC,WAAW,GAAG,EAAE,GAAI,UAAkB,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,EAAE,CAAC;IACnE,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAqEiB;gBAC9B,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,gFAAgF;yBAC9F;wBACD,gBAAgB,EAAE;4BAChB,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,yEAAyE;yBACvF;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,8FAA8F;yBAC5G;wBACD,eAAe,EAAE;4BACf,IAAI,EAAE,SAAS;4BACf,WAAW,EAAE,yFAAyF;yBACvG;wBACD,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,mDAAmD;4BAChE,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBACzC;wBACD,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,sFAAsF;yBACpG;qBACF;oBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;iBACtB;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAgC;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAEnD,oCAAoC;YACpC,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC;YAErE,8BAA8B;YAC9B,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAE5D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,UAAU,EAAE,CAAC,CAAC,CAAC;YAEvD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC7D,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,MAAM,EAAE,qCAAqC;wBAC7C,QAAQ,EAAE,CAAC;wBACX,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,gBAAgB,EAAE,UAAU;wBAC5B,aAAa,EAAE,CAAC;wBAChB,KAAK,EAAE,0BAA0B;qBAClC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,IAAI,MAA4D,CAAC;YAEjE,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;oBAClD,GAAG,EAAE,UAAU;oBACf,GAAG;oBACH,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;oBACjC,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,cAAc;iBAC3C,CAAC,CAAC;gBAEH,MAAM,GAAG;oBACP,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,EAAE;oBAC/B,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,EAAE;oBAC/B,QAAQ,EAAE,CAAC;iBACZ,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,qCAAqC;gBACrC,MAAM,GAAG;oBACP,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;oBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE;oBAC3C,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;iBAC1B,CAAC;YACJ,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;YAEtC,iDAAiD;YACjD,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;oBACtF,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;gBACjC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,kDAAkD;gBACpD,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAE1D,OAAO;gBACL,OAAO;gBACP,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;gBAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,gBAAgB,EAAE,UAAU;gBAC5B,aAAa;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAc,CAAC;YAEjC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAEjE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAG,SAAiB,CAAC,MAAM,IAAI,EAAE;gBACvC,MAAM,EAAG,SAAiB,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO;gBACtD,QAAQ,EAAG,SAAiB,CAAC,QAAQ,IAAI,CAAC;gBAC1C,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB;gBACnE,aAAa;gBACb,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,QAAkB,EAAE,gBAAyB;QACnE,MAAM,OAAO,GAAsB,EAAE,CAAC;QAEtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC;gBACvC,OAAO;gBACP,gBAAgB,EAAE,gBAAgB,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,WAAW,EAAE,EAAE;gBACf,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,KAAK;aACvB,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,oCAAoC;YACpC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,0CAA0C,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC5E,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAkB,EAAE,gBAAyB;QACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CACtC,IAAI,CAAC,cAAc,CAAC;YAClB,OAAO;YACP,gBAAgB,EAAE,gBAAgB,IAAI,IAAI,CAAC,gBAAgB;YAC3D,WAAW,EAAE,EAAE;YACf,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,KAAK;SACvB,CAAC,CACH,CAAC;QAEF,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe;QAC3C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC,CAAC;QAEzE,8DAA8D;QAC9D,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAAC,MAAW,EAAE,aAAqB,EAAE,OAAgB;QAC/E,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,qCAAqC,aAAa,KAAK,CAAC,CAAC,CAAC;QACpF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,mCAAmC,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,CAAC;QACpG,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,UAAkB;QACvD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC,CAAC,gBAAgB;QAC/B,CAAC;QAED,yCAAyC;QACzC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,mBAAmB,CAAC,SAAiB;QACnC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACjC,CAAC;IAED,sBAAsB,CAAC,GAAW,EAAE,KAAa;QAC/C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,OAAe;QAC9B,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,WAAW;YAC5B,WAAW,EAAE,UAAU;YACvB,gBAAgB,EAAE,0BAA0B;YAC5C,MAAM,EAAE,oBAAoB;YAC5B,oBAAoB,EAAE,oBAAoB;YAC1C,eAAe,EAAE,YAAY;YAC7B,SAAS,EAAE,mBAAmB;YAC9B,iBAAiB,EAAE,uCAAuC;YAC1D,iBAAiB,EAAE,qCAAqC;YACxD,4BAA4B,EAAE,6BAA6B;YAC3D,+BAA+B,EAAE,qBAAqB;YACtD,UAAU,EAAE,mBAAmB;YAC/B,OAAO,EAAE,oBAAoB;YAC7B,QAAQ,EAAE,oBAAoB;YAC9B,cAAc,EAAE,mBAAmB;YACnC,QAAQ,EAAE,aAAa;YACvB,WAAW,EAAE,0BAA0B;YACvC,OAAO,EAAE,4BAA4B;YACrC,SAAS,EAAE,wBAAwB;YACnC,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,kBAAkB;YAC9B,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,kBAAkB;YAC9B,UAAU,EAAE,kBAAkB;YAC9B,UAAU,EAAE,gBAAgB;SAC7B,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO;YACL,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAC/D,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI;YAChE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;YAClE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAChE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;YAC3D,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY;YAC/D,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa;SAClE,CAAC;IACJ,CAAC;CACF"}