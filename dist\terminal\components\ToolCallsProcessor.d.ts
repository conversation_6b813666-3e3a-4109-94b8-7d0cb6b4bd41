import { EventEmitter } from 'events';
import { ConfigManager } from '../../config/ConfigManager.js';
import { ShellTool } from '../../tools/ShellTool.js';
import { <PERSON>rrorHandler } from '../../utils/ErrorHandler.js';
import { ToolCall } from '../../types/index.js';
export interface ToolExecutionResult {
    toolCall: ToolCall;
    result: any;
    success: boolean;
    executionTime: number;
    error?: string;
    requiresApproval?: boolean;
    approved?: boolean;
}
export interface ToolProcessorOptions {
    autoApprove: boolean;
    enableParallelExecution: boolean;
    maxConcurrentTools: number;
    executionTimeout: number;
    requireApprovalForDangerous: boolean;
}
export declare class ToolCallsProcessor extends EventEmitter {
    private configManager;
    private shellTool;
    private errorHandler;
    private options;
    private executionQueue;
    private activeExecutions;
    constructor(configManager: Config<PERSON>anager, shellTool: ShellTool, errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, options?: Partial<ToolProcessorOptions>);
    /**
     * Process multiple tool calls
     */
    processToolCalls(toolCalls: ToolCall[]): Promise<ToolExecutionResult[]>;
    /**
     * Execute tool calls sequentially
     */
    private executeSequential;
    /**
     * Execute tool calls in parallel
     */
    private executeParallel;
    /**
     * Execute a single tool call
     */
    private executeSingleTool;
    /**
     * Execute specific tool function
     */
    private executeToolFunction;
    /**
     * Execute shell command tool
     */
    private executeShellCommand;
    /**
     * Check if tool requires approval
     */
    private requiresApproval;
    /**
     * Check if a command is dangerous and requires approval
     */
    private isDangerousCommand;
    /**
     * Request user approval for tool execution
     */
    private requestApproval;
    /**
     * Check if tool is critical (failure should stop execution)
     */
    private isCriticalTool;
    /**
     * Create execution batches for parallel processing
     */
    private createExecutionBatches;
    /**
     * Format tool execution results for display
     */
    formatResults(results: ToolExecutionResult[]): string;
    /**
     * Format specific tool result
     */
    private formatToolResult;
    /**
     * Format shell command result
     */
    private formatShellResult;
    /**
     * Get execution statistics
     */
    getExecutionStats(): {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageExecutionTime: number;
        approvalRequests: number;
        approvedRequests: number;
    };
    /**
     * Update processor options
     */
    updateOptions(options: Partial<ToolProcessorOptions>): void;
}
//# sourceMappingURL=ToolCallsProcessor.d.ts.map