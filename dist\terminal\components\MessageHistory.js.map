{"version": 3, "file": "MessageHistory.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/MessageHistory.ts"], "names": [], "mappings": "AAGA,oCAAoC;AACpC,MAAM,KAAK,GAAG;IACZ,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAClD,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,kBAAkB,IAAI,SAAS,EAAE;IACjE,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAChD,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAChD,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;CAClD,CAAC;AAWF,MAAM,OAAO,cAAc;IACjB,OAAO,GAAmB,EAAE,CAAC;IAC7B,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,aAAa,CAAgB;IAC7B,cAAc,GAAG,IAAI,CAAC;IAE9B,YAAY,aAA4B;QACtC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEM,QAAQ,CAAC,OAAoB,EAAE,QAAsB,EAAE,SAAkB;QAC9E,MAAM,KAAK,GAAiB;YAC1B,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,SAAS,IAAI,SAAS;YACjC,OAAO;YACP,QAAQ;YACR,aAAa,EAAE,SAAS,CAAC,4CAA4C;SACtE,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAExC,sCAAsC;QACtC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;QAED,OAAO,KAAK,CAAC,EAAE,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,EAAU;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,aAAa;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEM,oBAAoB,CAAC,SAAiB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;IACrE,CAAC;IAEM,gBAAgB,CAAC,KAAa;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAEM,aAAa,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YACxD,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAC7D,CAAC;IACJ,CAAC;IAEM,eAAe,CAAC,SAAwB;QAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE3C,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC;IAEM,sBAAsB;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEM,sBAAsB;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC1C,CAAC;IAEM,YAAY;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEM,mBAAmB,CAAC,SAAiB;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QAC3E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACxC,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEM,aAAa,CAAC,SAA0B,MAAM;QACnD,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;gBACnD,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/D,MAAM,IAAI,GAAG,YAAY,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACjD,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpE,OAAO,GAAG,SAAS,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,OAAO,CAAC;YACvD,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,SAA0B,MAAM;QACjE,IAAI,CAAC;YACH,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAmB,CAAC;gBACpD,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,kEAAkE;gBAClE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,oBAAoB;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAE3E,oBAAoB;YACpB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAExC,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEM,aAAa;QAQlB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;gBACL,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,CAAC;gBAChB,mBAAmB,EAAE,CAAC;gBACtB,iBAAiB,EAAE,IAAI;gBACvB,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI;aAClB,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACrF,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO;aAC/B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC;aACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,CAAa,CAAC;QAElD,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC;YAClD,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM;YAC3E,CAAC,CAAC,CAAC,CAAC;QAEN,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,GAAG,EAAkB,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,iBAAiB,GAAkB,IAAI,CAAC;QAC5C,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YACzC,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;gBACrB,QAAQ,GAAG,KAAK,CAAC;gBACjB,iBAAiB,GAAG,SAAS,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5E,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YACjC,aAAa,EAAE,QAAQ,CAAC,IAAI;YAC5B,mBAAmB;YACnB,iBAAiB;YACjB,WAAW;YACX,WAAW;SACZ,CAAC;IACJ,CAAC;IAEM,uBAAuB,CAAC,OAAwB;QACrD,MAAM,aAAa,GAAG,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAE3D,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvD,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE;gBACzD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC;gBAC1D,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS;gBACjC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC;gBACrC,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,SAAS,GAAG,WAAW,IAAI,CAAC;YACxE,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC;YAEhE,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACxI,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,aAAa,MAAM,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,UAAU;QAChB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzE,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YAAE,OAAO;QAEpD,IAAI,CAAC;YACH,4CAA4C;YAC5C,wCAAwC;YACxC,+DAA+D;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wBAAwB;QAC1B,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YAAE,OAAO;QAEpD,IAAI,CAAC;YACH,wCAAwC;YACxC,wCAAwC;YACxC,6DAA6D;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;QACzB,CAAC;IACH,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;CACF"}