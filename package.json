{"name": "arien-ai-cli", "version": "1.0.0", "description": "Modern and powerful CLI terminal system with LLM integration for executing shell commands intelligently", "type": "module", "main": "dist/index.js", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "install-global": "npm run build && npm install -g .", "uninstall-global": "npm uninstall -g arien-ai-cli", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "type-check": "tsc --noEmit", "setup": "node scripts/install.js", "doctor": "node dist/index.js doctor", "prebuild": "npm run clean", "postbuild": "echo 'Build completed successfully!'", "prepare": "npm run build"}, "keywords": ["cli", "terminal", "ai", "llm", "shell", "automation", "deepseek", "ollama"], "author": "Arien AI", "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"commander": "^12.0.0", "inquirer": "^9.2.15", "chalk": "^5.3.0", "ora": "^8.0.1", "boxen": "^7.1.1", "figlet": "^1.7.0", "node-fetch": "^3.3.2", "ws": "^8.16.0", "blessed": "^0.1.81", "blessed-contrib": "^4.11.0", "strip-ansi": "^7.1.0", "ansi-escapes": "^6.2.0", "terminal-kit": "^3.0.1", "execa": "^8.0.1", "cross-spawn": "^7.0.3", "tree-kill": "^1.2.2", "conf": "^12.0.0", "keytar": "^7.9.0", "dotenv": "^16.4.1", "lodash": "^4.17.21", "rxjs": "^7.8.1"}, "devDependencies": {"@types/node": "^20.11.19", "@types/inquirer": "^9.0.7", "@types/figlet": "^1.5.8", "@types/blessed": "^0.1.25", "@types/ws": "^8.5.10", "@types/cross-spawn": "^6.0.6", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "eslint": "^8.56.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tsx": "^4.7.1", "typescript": "^5.8.3", "jest": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-ai-cli.git"}, "bugs": {"url": "https://github.com/arien-ai/arien-ai-cli/issues"}, "homepage": "https://github.com/arien-ai/arien-ai-cli#readme"}