import { EventEmitter } from 'events';
import { ConfigManager } from '../../config/ConfigManager.js';
import { MessageHistory } from './MessageHistory.js';
import { SlashCommands } from './SlashCommands.js';
import chalk from 'chalk';

export interface InputProcessorOptions {
  enableHistory: boolean;
  enableAutoComplete: boolean;
  enableSlashCommands: boolean;
  maxInputLength: number;
}

export interface ProcessedInput {
  type: 'message' | 'command' | 'empty';
  content: string;
  isSlashCommand: boolean;
  command?: string;
  args?: string[];
  metadata?: Record<string, any>;
}

export class ChatInputProcessor extends EventEmitter {
  private configManager: ConfigManager;
  private messageHistory: MessageHistory;
  private slashCommands: SlashCommands;
  private options: InputProcessorOptions;
  private inputBuffer: string = '';
  private historyIndex: number = -1;
  private autoCompleteCache: Map<string, string[]> = new Map();

  constructor(
    configManager: ConfigManager,
    messageHistory: MessageHistory,
    slashCommands: SlashCommands,
    options: Partial<InputProcessorOptions> = {}
  ) {
    super();
    this.configManager = configManager;
    this.messageHistory = messageHistory;
    this.slashCommands = slashCommands;
    this.options = {
      enableHistory: true,
      enableAutoComplete: true,
      enableSlashCommands: true,
      maxInputLength: 4096,
      ...options
    };
  }

  /**
   * Process user input and return structured result
   */
  public processInput(input: string): ProcessedInput {
    // Validate input length
    if (input.length > this.options.maxInputLength) {
      throw new Error(`Input too long. Maximum length is ${this.options.maxInputLength} characters.`);
    }

    // Trim and normalize input
    const normalizedInput = input.trim();

    // Handle empty input
    if (!normalizedInput) {
      return {
        type: 'empty',
        content: '',
        isSlashCommand: false
      };
    }

    // Check if it's a slash command
    if (this.options.enableSlashCommands && this.slashCommands.isSlashCommand(normalizedInput)) {
      return this.processSlashCommand(normalizedInput);
    }

    // Process as regular message
    return this.processMessage(normalizedInput);
  }

  /**
   * Process slash command input
   */
  private processSlashCommand(input: string): ProcessedInput {
    const parts = input.split(' ');
    const command = parts[0].substring(1); // Remove the '/' prefix
    const args = parts.slice(1);

    return {
      type: 'command',
      content: input,
      isSlashCommand: true,
      command,
      args,
      metadata: {
        timestamp: new Date(),
        source: 'user'
      }
    };
  }

  /**
   * Process regular message input
   */
  private processMessage(input: string): ProcessedInput {
    return {
      type: 'message',
      content: input,
      isSlashCommand: false,
      metadata: {
        timestamp: new Date(),
        source: 'user',
        length: input.length,
        wordCount: input.split(/\s+/).length
      }
    };
  }

  /**
   * Handle history navigation
   */
  public navigateHistory(direction: 'up' | 'down'): string | null {
    if (!this.options.enableHistory) {
      return null;
    }

    const history = this.messageHistory.getRecentEntries(50);
    if (history.length === 0) {
      return null;
    }

    if (direction === 'up') {
      this.historyIndex = Math.min(this.historyIndex + 1, history.length - 1);
    } else {
      this.historyIndex = Math.max(this.historyIndex - 1, -1);
    }

    if (this.historyIndex === -1) {
      return this.inputBuffer;
    }

    return history[history.length - 1 - this.historyIndex].message.content;
  }

  /**
   * Get auto-complete suggestions
   */
  public getAutoCompleteSuggestions(input: string): string[] {
    if (!this.options.enableAutoComplete) {
      return [];
    }

    const suggestions: string[] = [];

    // Slash command auto-complete
    if (input.startsWith('/')) {
      const slashSuggestions = this.slashCommands.getAutoCompleteOptions(input);
      suggestions.push(...slashSuggestions);
    }

    // History-based auto-complete
    const historySuggestions = this.getHistoryBasedSuggestions(input);
    suggestions.push(...historySuggestions);

    // Remove duplicates and sort
    return [...new Set(suggestions)].sort();
  }

  /**
   * Get suggestions based on message history
   */
  private getHistoryBasedSuggestions(input: string): string[] {
    const cacheKey = input.toLowerCase();
    
    if (this.autoCompleteCache.has(cacheKey)) {
      return this.autoCompleteCache.get(cacheKey)!;
    }

    const suggestions: string[] = [];
    const history = this.messageHistory.getRecentEntries(100);
    const inputLower = input.toLowerCase();

    for (const entry of history) {
      const content = entry.message.content;
      
      // Find messages that start with the input
      if (content.toLowerCase().startsWith(inputLower) && content.length > input.length) {
        suggestions.push(content);
      }

      // Find words that start with the last word of input
      const words = input.split(' ');
      const lastWord = words[words.length - 1].toLowerCase();
      
      if (lastWord.length > 2) {
        const contentWords = content.split(' ');
        for (const word of contentWords) {
          if (word.toLowerCase().startsWith(lastWord) && word.length > lastWord.length) {
            const suggestion = words.slice(0, -1).concat(word).join(' ');
            suggestions.push(suggestion);
          }
        }
      }
    }

    // Cache the results
    const uniqueSuggestions = [...new Set(suggestions)].slice(0, 10);
    this.autoCompleteCache.set(cacheKey, uniqueSuggestions);
    
    return uniqueSuggestions;
  }

  /**
   * Set input buffer for history navigation
   */
  public setInputBuffer(input: string): void {
    this.inputBuffer = input;
    this.historyIndex = -1;
  }

  /**
   * Clear auto-complete cache
   */
  public clearAutoCompleteCache(): void {
    this.autoCompleteCache.clear();
  }

  /**
   * Validate input for potential security issues
   */
  public validateInput(input: string): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /sudo\s+rm/, // sudo rm
      />\s*\/dev\/sda/, // Writing to disk devices
      /mkfs/, // Format filesystem
      /dd\s+if=.*of=\/dev/, // Direct disk write
      /:(){ :|:& };:/, // Fork bomb
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(input)) {
        warnings.push(`Potentially dangerous command detected: ${pattern.source}`);
      }
    }

    // Check for very long commands
    if (input.length > 1000) {
      warnings.push('Very long command detected');
    }

    // Check for suspicious characters
    if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(input)) {
      warnings.push('Control characters detected in input');
    }

    return {
      isValid: warnings.length === 0,
      warnings
    };
  }

  /**
   * Format input for display
   */
  public formatInputForDisplay(input: ProcessedInput): string {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = chalk.blue(`[${timestamp}] 👤 You:`);
    
    if (input.isSlashCommand) {
      return `${prefix} ${chalk.cyan(input.content)}`;
    }
    
    return `${prefix} ${input.content}`;
  }

  /**
   * Get input statistics
   */
  public getInputStats(): {
    totalInputs: number;
    averageLength: number;
    commandCount: number;
    messageCount: number;
  } {
    const history = this.messageHistory.getRecentEntries(1000);
    const userMessages = history.filter(entry => entry.message.role === 'user');
    
    const commandCount = userMessages.filter(entry => 
      entry.message.content.startsWith('/')
    ).length;
    
    const messageCount = userMessages.length - commandCount;
    const totalLength = userMessages.reduce((sum, entry) => 
      sum + entry.message.content.length, 0
    );
    
    return {
      totalInputs: userMessages.length,
      averageLength: userMessages.length > 0 ? Math.round(totalLength / userMessages.length) : 0,
      commandCount,
      messageCount
    };
  }
}
