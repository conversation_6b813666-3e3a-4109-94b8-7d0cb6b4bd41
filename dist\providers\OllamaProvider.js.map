{"version": 3, "file": "OllamaProvider.js", "sourceRoot": "", "sources": ["../../src/providers/OllamaProvider.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,YAAY,CAAC;AAQ/B,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM,OAAO,cAAc;IACzB,IAAI,GAAG,QAAQ,CAAC;IAChB,MAAM,GAAa,EAAE,CAAC;IACtB,OAAO,CAAS;IACR,YAAY,CAAe;IAEnC,YAAY,OAAO,GAAG,wBAAwB,EAAE,YAA2B;QACzE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,YAAY,CAAC;YACnD,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,CAAC;YACpB,eAAe,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,WAAW,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,KAAK;gBACb,2DAA2D;aAC5D,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC;YACzD,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;gBAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,2CAA2C;QAC7C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAuB,EAAE,OAA2B;QACzE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG;YAClB,KAAK;YACL,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvC,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;gBACxC,WAAW,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;aACxC;YACD,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI;gBACpB,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;aACvC,CAAC;SACH,CAAC;QAEF,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAChC,KAAK,IAAI,EAAE;YACT,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,MAAO,SAAiB,EAAE,KAAK,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE;gBACpC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;gBAC3D,KAAK,EAAE;oBACL,YAAY,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC;oBACzC,gBAAgB,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;oBACtC,WAAW,EAAE,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;iBACpE;aACF,CAAC;QACJ,CAAC,EACD;YACE,SAAS,EAAE,0BAA0B;YACrC,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE,CAAC;SACd,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,CAAC,cAAc,CAAC,QAAuB,EAAE,OAA2B;QACxE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG;YAClB,KAAK;YACL,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvC,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE;gBACP,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;gBACxC,WAAW,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;aACxC;YACD,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI;gBACpB,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;aACvC,CAAC;SACH,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAChD,KAAK,IAAI,EAAE;YACT,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBAClD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;gBACZ,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrD,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,CAAC,MAAM,MAAO,SAAiB,EAAE,KAAK,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;YACtG,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EACD;YACE,SAAS,EAAE,wBAAwB;YACnC,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE,CAAC;SACd,CACF,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,MAAM,GAAI,QAAQ,CAAC,IAAY,EAAE,SAAS,EAAE,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,CAAC;YACH,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAE5C,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;oBACrB,MAAM;gBACR,CAAC;gBAED,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC5B,IAAI,CAAC,OAAO;wBAAE,SAAS;oBAEvB,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;wBAEnC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;4BAChB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;4BACrB,OAAO;wBACT,CAAC;wBAED,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;4BAC5B,MAAM;gCACJ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gCAC/B,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gCACtD,IAAI,EAAE,KAAK;6BACZ,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,0BAA0B;wBAC1B,SAAS;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAuB;QAC5C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,mCAAmC;YAClF,OAAO,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM;gBAC1B,CAAC,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE;gBAC/B,CAAC,CAAC,GAAG,CAAC,OAAO;SAChB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,WAAW,CAAC,KAAY;QAC9B,2DAA2D;QAC3D,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;gBACtC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;aACrC;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,sCAAsC;QACtC,iFAAiF;QACjF,MAAM,aAAa,GAAG,8BAA8B,CAAC;QACrD,MAAM,SAAS,GAAU,EAAE,CAAC;QAC5B,IAAI,KAA6B,CAAC;QAElC,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACtD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAC5C,SAAS,CAAC,IAAI,CAAC;wBACb,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBACnE,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE;4BACR,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;4BAC1B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC;yBAC3D;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,oBAAoB;YACtB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,QAAQ,CAAC,CAAC,iBAAiB;QACpC,CAAC;QAED,mDAAmD;QACnD,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAErE,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACrC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CACxC,CAAC;YACF,IAAI,KAAK;gBAAE,OAAO,KAAK,CAAC;QAC1B,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,OAAe;QACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAiB;QAC/B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;YACvD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;YACzD,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,+BAA+B;QAC/B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED,eAAe;QACb,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yHA+B8G,CAAC;IACxH,CAAC;CACF"}