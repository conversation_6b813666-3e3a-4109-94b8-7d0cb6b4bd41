import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import chalk from 'chalk';
import { Tool, ShellToolResult, CommandExecutionContext } from '../types/index.js';
import { <PERSON>rror<PERSON>andler } from '../utils/ErrorHandler.js';

const execAsync = promisify(exec);

// Platform detection
const platform = () => {
  if (typeof globalThis !== 'undefined' && (globalThis as any).process) {
    return (globalThis as any).process.platform;
  }
  return 'unknown';
};

export class ShellTool {
  private errorHandler: ErrorHandler;
  private currentDirectory: string;
  private environment: Record<string, string>;

  constructor(errorHandler: ErrorHandler) {
    this.errorHandler = errorHandler;
    this.currentDirectory = (globalThis as any).process?.cwd?.() || '.';
    this.environment = { ...(globalThis as any).process?.env || {} };
  }

  /**
   * Get the tool definition for LLM function calling
   * This provides comprehensive information about when and how to use the shell tool
   */
  getToolDefinition(): Tool {
    return {
      type: 'function',
      function: {
        name: 'execute_shell_command',
        description: `Execute shell commands with intelligent error handling and output processing.

WHEN TO USE:
- File system operations (create, read, write, delete files/directories)
- System information gathering (ps, top, df, ls, dir)
- Package management (npm, pip, apt, brew, etc.)
- Git operations (clone, commit, push, pull, status)
- Process management (start/stop services, kill processes)
- Network operations (curl, wget, ping, netstat)
- Development tasks (build, test, deploy)
- System administration tasks
- Any command-line tool execution

WHEN NOT TO USE:
- For simple text processing that can be done programmatically
- For operations that require interactive input (use non-interactive flags)
- For extremely long-running processes without progress indicators
- For commands that might cause system damage without user approval

USAGE PATTERNS:

1. SEQUENTIAL EXECUTION (Default):
   Use for commands that depend on each other:
   - First: "mkdir project && cd project"
   - Then: "npm init -y"
   - Finally: "npm install express"

2. PARALLEL EXECUTION:
   Use for independent operations:
   - Multiple file downloads
   - Parallel test runs
   - Independent system checks

3. CONDITIONAL EXECUTION:
   Check command success before proceeding:
   - "test -f package.json && npm install"
   - "which docker && docker --version"

BEST PRACTICES:
- Always check command exit codes
- Use absolute paths when possible
- Include error handling for critical operations
- Provide meaningful command descriptions
- Use appropriate timeouts for long operations
- Consider user approval for destructive operations

EXAMPLES:

File Operations:
- "ls -la" - List directory contents
- "find . -name '*.js' -type f" - Find JavaScript files
- "cat package.json" - Read file contents
- "mkdir -p src/components" - Create directories

System Information:
- "ps aux | grep node" - Find Node.js processes
- "df -h" - Check disk usage
- "free -m" - Check memory usage
- "uname -a" - System information

Development:
- "npm run build" - Build project
- "git status" - Check Git status
- "docker ps" - List containers
- "python -m pytest tests/" - Run tests

Network:
- "curl -I https://api.example.com" - Check API status
- "ping -c 4 google.com" - Test connectivity
- "netstat -tulpn" - Check open ports`,
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: 'The shell command to execute. Use proper shell syntax for the target platform.'
            },
            workingDirectory: {
              type: 'string',
              description: 'Working directory for command execution. Defaults to current directory.'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds. Default: 30000 (30 seconds). Use higher values for long operations.'
            },
            requireApproval: {
              type: 'boolean',
              description: 'Whether to require user approval before execution. Use true for destructive operations.'
            },
            environment: {
              type: 'object',
              description: 'Additional environment variables for the command.',
              additionalProperties: { type: 'string' }
            },
            description: {
              type: 'string',
              description: 'Human-readable description of what this command does. Helps with user understanding.'
            }
          },
          required: ['command']
        }
      }
    };
  }

  /**
   * Execute a shell command with comprehensive error handling and output processing
   */
  async executeCommand(context: CommandExecutionContext): Promise<ShellToolResult> {
    const startTime = Date.now();
    
    try {
      // Validate the command
      this.errorHandler.validateCommand(context.command);

      // Set working directory if provided
      const workingDir = context.workingDirectory || this.currentDirectory;
      
      // Merge environment variables
      const env = { ...this.environment, ...context.environment };

      console.log(chalk.blue(`\n🔧 Executing: ${context.command}`));
      console.log(chalk.gray(`   Directory: ${workingDir}`));
      
      if (context.requireApproval) {
        const approved = await this.requestApproval(context.command);
        if (!approved) {
          return {
            success: false,
            stdout: '',
            stderr: 'Command execution cancelled by user',
            exitCode: 1,
            command: context.command,
            workingDirectory: workingDir,
            executionTime: 0,
            error: 'User cancelled execution'
          };
        }
      }

      // Execute the command using built-in exec
      let result: { stdout: string; stderr: string; exitCode: number };

      try {
        const execResult = await execAsync(context.command, {
          cwd: workingDir,
          env,
          timeout: context.timeout || 30000,
          maxBuffer: 1024 * 1024 * 10 // 10MB buffer
        });

        result = {
          stdout: execResult.stdout || '',
          stderr: execResult.stderr || '',
          exitCode: 0
        };
      } catch (error: any) {
        // Handle execution errors gracefully
        result = {
          stdout: error.stdout || '',
          stderr: error.stderr || error.message || '',
          exitCode: error.code || 1
        };
      }

      const executionTime = Date.now() - startTime;
      const success = result.exitCode === 0;

      // Update current directory if command changed it
      if (context.command.startsWith('cd ')) {
        try {
          const newDir = this.resolveDirectory(context.command.substring(3).trim(), workingDir);
          this.currentDirectory = newDir;
        } catch (error) {
          // Directory change failed, keep current directory
        }
      }

      // Format output
      this.displayCommandResult(result, executionTime, success);

      return {
        success,
        stdout: result.stdout || '',
        stderr: result.stderr || '',
        exitCode: result.exitCode || 0,
        command: context.command,
        workingDirectory: workingDir,
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const execError = error as Error;

      this.errorHandler.handleCommandError(execError, context.command);

      return {
        success: false,
        stdout: (execError as any).stdout || '',
        stderr: (execError as any).stderr || execError.message,
        exitCode: (execError as any).exitCode || 1,
        command: context.command,
        workingDirectory: context.workingDirectory || this.currentDirectory,
        executionTime,
        error: execError.message
      };
    }
  }

  /**
   * Execute multiple commands in sequence
   */
  async executeSequential(commands: string[], workingDirectory?: string): Promise<ShellToolResult[]> {
    const results: ShellToolResult[] = [];
    
    for (const command of commands) {
      const result = await this.executeCommand({
        command,
        workingDirectory: workingDirectory || this.currentDirectory,
        environment: {},
        timeout: 30000,
        requireApproval: false
      });
      
      results.push(result);
      
      // Stop execution if a command fails
      if (!result.success) {
        console.log(chalk.red(`\n❌ Command failed, stopping sequence: ${command}`));
        break;
      }
    }
    
    return results;
  }

  /**
   * Execute multiple commands in parallel
   */
  async executeParallel(commands: string[], workingDirectory?: string): Promise<ShellToolResult[]> {
    const promises = commands.map(command => 
      this.executeCommand({
        command,
        workingDirectory: workingDirectory || this.currentDirectory,
        environment: {},
        timeout: 30000,
        requireApproval: false
      })
    );
    
    return Promise.all(promises);
  }

  private async requestApproval(command: string): Promise<boolean> {
    console.log(chalk.yellow(`\n⚠️  Command requires approval: ${command}`));
    console.log(chalk.gray('This command may modify your system or files.'));
    
    // In a real implementation, you would use inquirer or similar
    // For now, we'll assume approval is granted
    return true;
  }

  private displayCommandResult(result: any, executionTime: number, success: boolean): void {
    if (success) {
      console.log(chalk.green(`✅ Command completed successfully (${executionTime}ms)`));
    } else {
      console.log(chalk.red(`❌ Command failed with exit code ${result.exitCode} (${executionTime}ms)`));
    }

    if (result.stdout) {
      console.log(chalk.white('\nOutput:'));
      console.log(result.stdout);
    }

    if (result.stderr) {
      console.log(chalk.yellow('\nErrors/Warnings:'));
      console.log(result.stderr);
    }
  }

  private resolveDirectory(path: string, currentDir: string): string {
    if (path.startsWith('/') || (platform() === 'win32' && path.match(/^[A-Za-z]:/))) {
      return path; // Absolute path
    }
    
    // Relative path resolution would go here
    return currentDir;
  }

  getCurrentDirectory(): string {
    return this.currentDirectory;
  }

  setCurrentDirectory(directory: string): void {
    this.currentDirectory = directory;
  }

  getEnvironment(): Record<string, string> {
    return { ...this.environment };
  }

  setEnvironmentVariable(key: string, value: string): void {
    this.environment[key] = value;
  }

  /**
   * Check if a command requires user approval before execution
   */
  requiresApproval(command: string): boolean {
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /sudo\s+rm/, // sudo rm
      />\s*\/dev\/sda/, // Writing to disk devices
      /mkfs/, // Format filesystem
      /dd\s+if=.*of=\/dev/, // Direct disk write
      /:(){ :|:& };:/, // Fork bomb
      /sudo\s+/, // Any sudo command
      /chmod\s+.*\/etc/, // Changing permissions on system files
      /chown\s+.*\/etc/, // Changing ownership of system files
      /systemctl\s+(stop|disable)/, // Stopping critical services
      /service\s+.*\s+(stop|restart)/, // Service management
      /iptables/, // Firewall changes
      /fdisk/, // Disk partitioning
      /parted/, // Disk partitioning
      /mount.*\/dev/, // Mounting devices
      /umount/, // Unmounting
      /kill\s+-9/, // Force killing processes
      /pkill/, // Killing processes by name
      /killall/, // Killing all processes
      /reboot/, // System reboot
      /shutdown/, // System shutdown
      /halt/, // System halt
      /poweroff/, // System poweroff
      /init\s+0/, // System shutdown
      /init\s+6/, // System reboot
    ];

    return dangerousPatterns.some(pattern => pattern.test(command));
  }

  /**
   * Get a list of safe commands that don't require approval
   */
  getSafeCommands(): string[] {
    return [
      'ls', 'dir', 'pwd', 'cd', 'cat', 'head', 'tail', 'grep', 'find',
      'ps', 'top', 'htop', 'df', 'du', 'free', 'uname', 'whoami', 'id',
      'date', 'uptime', 'which', 'whereis', 'file', 'stat', 'wc', 'sort',
      'uniq', 'cut', 'awk', 'sed', 'tr', 'tee', 'less', 'more', 'echo',
      'printf', 'history', 'alias', 'type', 'help', 'man', 'info',
      'git status', 'git log', 'git diff', 'git branch', 'git remote',
      'npm list', 'npm outdated', 'pip list', 'pip show', 'cargo check'
    ];
  }
}
