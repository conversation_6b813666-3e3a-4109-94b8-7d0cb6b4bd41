#!/usr/bin/env node
import { Command } from 'commander';
import { TerminalInterface } from './terminal/TerminalInterface.js';
import { ConfigManager } from './config/ConfigManager.js';
import { DeepseekProvider } from './providers/DeepseekProvider.js';
import { OllamaProvider } from './providers/OllamaProvider.js';
import { ErrorHandler } from './utils/ErrorHandler.js';
import chalk from 'chalk';
import figlet from 'figlet';
import boxen from 'boxen';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Read package.json for version
let packageJson;
try {
    packageJson = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));
}
catch {
    packageJson = { version: '1.0.0' };
}
const program = new Command();
program
    .name('arien')
    .description('Arien AI - Intelligent CLI Assistant with LLM integration')
    .version(packageJson.version);
// Interactive mode (default)
program
    .command('interactive', { isDefault: true })
    .alias('i')
    .description('Start interactive terminal mode')
    .option('-p, --provider <provider>', 'AI provider (deepseek, ollama)')
    .option('-m, --model <model>', 'AI model to use')
    .option('-k, --api-key <key>', 'API key for the provider')
    .option('-u, --base-url <url>', 'Base URL for the provider')
    .option('-t, --temperature <temp>', 'Temperature for AI responses (0.0-2.0)')
    .option('--auto-approve', 'Auto-approve shell commands (use with caution)')
    .option('--theme <theme>', 'UI theme (dark, light)')
    .action(async (options) => {
    try {
        await startInteractiveMode(options);
    }
    catch (error) {
        console.error(chalk.red('Error starting interactive mode:'), error.message);
        process.exit(1);
    }
});
// Configuration commands
const configCmd = program
    .command('config')
    .description('Manage configuration');
configCmd
    .command('show')
    .description('Show current configuration')
    .action(showConfig);
configCmd
    .command('set-api-key <key>')
    .description('Set API key for current provider')
    .action(setApiKey);
configCmd
    .command('set-provider <provider>')
    .description('Set AI provider (deepseek, ollama)')
    .action(setProvider);
configCmd
    .command('set-model <model>')
    .description('Set AI model')
    .action(setModel);
configCmd
    .command('reset')
    .description('Reset configuration to defaults')
    .action(resetConfig);
// Provider management commands
const providerCmd = program
    .command('provider')
    .description('Manage AI providers');
providerCmd
    .command('list')
    .description('List available providers')
    .action(listProviders);
providerCmd
    .command('test <provider>')
    .description('Test provider connection')
    .action(testProvider);
// Model management commands
const modelCmd = program
    .command('models')
    .description('Manage AI models');
modelCmd
    .command('list [provider]')
    .description('List available models for provider')
    .action(listModels);
// Ollama specific commands
const ollamaCmd = program
    .command('ollama')
    .description('Ollama specific commands');
ollamaCmd
    .command('pull <model>')
    .description('Pull a model from Ollama')
    .action(pullOllamaModel);
ollamaCmd
    .command('remove <model>')
    .description('Remove a model from Ollama')
    .action(removeOllamaModel);
// Session management commands
const sessionCmd = program
    .command('session')
    .description('Manage sessions');
sessionCmd
    .command('list')
    .description('List all sessions')
    .action(listSessions);
sessionCmd
    .command('delete <id>')
    .description('Delete a session')
    .action(deleteSession);
// Utility commands
program
    .command('doctor')
    .description('Run system diagnostics')
    .action(runDiagnostics);
program
    .command('update')
    .description('Check for updates')
    .action(checkUpdates);
// Parse command line arguments
program.parse();
// Implementation functions
async function startInteractiveMode(options) {
    // Show banner
    showBanner();
    // Apply command line options to config
    const configManager = new ConfigManager();
    if (options.provider)
        configManager.setProvider(options.provider);
    if (options.model)
        configManager.setModel(options.model);
    if (options.apiKey)
        configManager.setApiKey(options.apiKey);
    if (options.baseUrl)
        configManager.setBaseUrl(options.baseUrl);
    if (options.temperature)
        configManager.setTemperature(parseFloat(options.temperature));
    if (options.autoApprove)
        configManager.setAutoApprove(true);
    if (options.theme)
        configManager.setTheme(options.theme);
    // Start terminal interface
    const terminal = new TerminalInterface();
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        terminal.destroy();
        console.log(chalk.green('\n👋 Goodbye!'));
        process.exit(0);
    });
    process.on('SIGTERM', () => {
        terminal.destroy();
        process.exit(0);
    });
    await terminal.start();
}
function showBanner() {
    console.clear();
    const banner = figlet.textSync('Arien AI', {
        font: 'ANSI Shadow',
        horizontalLayout: 'default',
        verticalLayout: 'default'
    });
    const boxedBanner = boxen(chalk.cyan(banner) + '\n\n' +
        chalk.white('Intelligent CLI Assistant with LLM Integration') + '\n' +
        chalk.gray(`Version ${packageJson.version}`) + '\n' +
        chalk.gray('Powered by Deepseek & Ollama'), {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        backgroundColor: 'black'
    });
    console.log(boxedBanner);
}
async function showConfig() {
    const configManager = new ConfigManager();
    const config = configManager.getConfig();
    console.log(chalk.cyan.bold('\n🔧 Current Configuration:\n'));
    console.log(`${chalk.yellow('Provider:')} ${chalk.green(config.provider)}`);
    console.log(`${chalk.yellow('Model:')} ${chalk.green(config.model)}`);
    console.log(`${chalk.yellow('Temperature:')} ${chalk.green(config.temperature.toString())}`);
    console.log(`${chalk.yellow('Max Tokens:')} ${chalk.green(config.maxTokens.toString())}`);
    console.log(`${chalk.yellow('Auto Approve:')} ${chalk.green(config.autoApprove.toString())}`);
    console.log(`${chalk.yellow('Theme:')} ${chalk.green(config.theme)}`);
    console.log(`${chalk.yellow('Show Timestamps:')} ${chalk.green(config.showTimestamps.toString())}`);
    console.log(`${chalk.yellow('Save History:')} ${chalk.green(config.saveHistory.toString())}`);
    if (config.apiKey) {
        const maskedKey = config.apiKey.substring(0, 8) + '...';
        console.log(`${chalk.yellow('API Key:')} ${chalk.green(maskedKey)}`);
    }
    if (config.baseUrl) {
        console.log(`${chalk.yellow('Base URL:')} ${chalk.green(config.baseUrl)}`);
    }
}
async function setApiKey(key) {
    const configManager = new ConfigManager();
    configManager.setApiKey(key);
    console.log(chalk.green('✅ API key set successfully'));
}
async function setProvider(provider) {
    const validProviders = ['deepseek', 'ollama'];
    if (!validProviders.includes(provider)) {
        console.error(chalk.red(`❌ Invalid provider. Valid options: ${validProviders.join(', ')}`));
        return;
    }
    const configManager = new ConfigManager();
    configManager.setProvider(provider);
    console.log(chalk.green(`✅ Provider set to: ${provider}`));
}
async function setModel(model) {
    const configManager = new ConfigManager();
    configManager.setModel(model);
    console.log(chalk.green(`✅ Model set to: ${model}`));
}
async function resetConfig() {
    const configManager = new ConfigManager();
    configManager.reset();
    console.log(chalk.green('✅ Configuration reset to defaults'));
}
async function listProviders() {
    const configManager = new ConfigManager();
    const currentProvider = configManager.getProvider();
    console.log(chalk.cyan.bold('\n🤖 Available Providers:\n'));
    const providers = [
        { name: 'deepseek', description: 'Deepseek AI (requires API key)' },
        { name: 'ollama', description: 'Ollama (local, requires Ollama running)' }
    ];
    providers.forEach(provider => {
        const current = provider.name === currentProvider ? chalk.green(' (current)') : '';
        console.log(`  ${chalk.yellow(provider.name)}${current} - ${chalk.gray(provider.description)}`);
    });
}
async function testProvider(providerName) {
    const configManager = new ConfigManager();
    const errorHandler = new ErrorHandler(configManager.getRetryConfig());
    console.log(chalk.blue(`🔍 Testing provider: ${providerName}`));
    try {
        let provider;
        if (providerName === 'deepseek') {
            provider = new DeepseekProvider(configManager.getApiKey(), errorHandler);
        }
        else if (providerName === 'ollama') {
            provider = new OllamaProvider(configManager.getBaseUrl() || 'http://localhost:11434', errorHandler);
        }
        else {
            console.error(chalk.red(`❌ Unknown provider: ${providerName}`));
            return;
        }
        const isAvailable = await provider.isAvailable();
        if (isAvailable) {
            console.log(chalk.green(`✅ Provider ${providerName} is available and working`));
        }
        else {
            console.log(chalk.red(`❌ Provider ${providerName} is not available`));
        }
    }
    catch (error) {
        console.error(chalk.red(`❌ Error testing provider: ${error.message}`));
    }
}
async function listModels(provider) {
    const configManager = new ConfigManager();
    const errorHandler = new ErrorHandler(configManager.getRetryConfig());
    const targetProvider = provider || configManager.getProvider();
    console.log(chalk.blue(`🔍 Fetching models for: ${targetProvider}`));
    try {
        let providerInstance;
        if (targetProvider === 'deepseek') {
            providerInstance = new DeepseekProvider(configManager.getApiKey(), errorHandler);
        }
        else if (targetProvider === 'ollama') {
            providerInstance = new OllamaProvider(configManager.getBaseUrl() || 'http://localhost:11434', errorHandler);
        }
        else {
            console.error(chalk.red(`❌ Unknown provider: ${targetProvider}`));
            return;
        }
        const models = await providerInstance.getModels();
        const currentModel = configManager.getModel();
        console.log(chalk.cyan.bold(`\n📋 Models for ${targetProvider}:\n`));
        if (models.length === 0) {
            console.log(chalk.yellow('  No models available'));
        }
        else {
            models.forEach(model => {
                const current = model === currentModel ? chalk.green(' (current)') : '';
                console.log(`  ${chalk.yellow(model)}${current}`);
            });
        }
    }
    catch (error) {
        console.error(chalk.red(`❌ Error fetching models: ${error.message}`));
    }
}
async function pullOllamaModel(model) {
    const configManager = new ConfigManager();
    const errorHandler = new ErrorHandler(configManager.getRetryConfig());
    console.log(chalk.blue(`📥 Pulling Ollama model: ${model}`));
    try {
        const ollama = new OllamaProvider(configManager.getBaseUrl() || 'http://localhost:11434', errorHandler);
        await ollama.pullModel(model);
        console.log(chalk.green(`✅ Model ${model} pulled successfully`));
    }
    catch (error) {
        console.error(chalk.red(`❌ Error pulling model: ${error.message}`));
    }
}
async function removeOllamaModel(model) {
    const configManager = new ConfigManager();
    const errorHandler = new ErrorHandler(configManager.getRetryConfig());
    console.log(chalk.blue(`🗑️  Removing Ollama model: ${model}`));
    try {
        const ollama = new OllamaProvider(configManager.getBaseUrl() || 'http://localhost:11434', errorHandler);
        await ollama.deleteModel(model);
        console.log(chalk.green(`✅ Model ${model} removed successfully`));
    }
    catch (error) {
        console.error(chalk.red(`❌ Error removing model: ${error.message}`));
    }
}
async function listSessions() {
    const configManager = new ConfigManager();
    const sessions = configManager.getSessions();
    const sessionEntries = Object.values(sessions);
    console.log(chalk.cyan.bold('\n📋 Sessions:\n'));
    if (sessionEntries.length === 0) {
        console.log(chalk.yellow('  No sessions found'));
    }
    else {
        sessionEntries.forEach(session => {
            console.log(`  ${chalk.green(session.name)} (${chalk.gray(session.id)})`);
            console.log(`    ${chalk.yellow(session.messageCount)} messages - ${chalk.blue(session.lastActivity.toLocaleString())}`);
            console.log(`    Provider: ${chalk.cyan(session.provider)} | Model: ${chalk.cyan(session.model)}\n`);
        });
    }
}
async function deleteSession(id) {
    const configManager = new ConfigManager();
    const session = configManager.getSession(id);
    if (!session) {
        console.error(chalk.red(`❌ Session not found: ${id}`));
        return;
    }
    configManager.deleteSession(id);
    console.log(chalk.green(`✅ Session deleted: ${session.name} (${id})`));
}
async function runDiagnostics() {
    console.log(chalk.cyan.bold('\n🔍 Running System Diagnostics...\n'));
    const configManager = new ConfigManager();
    const config = configManager.getConfig();
    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`${chalk.yellow('Node.js Version:')} ${chalk.green(nodeVersion)}`);
    // Check configuration
    const configValidation = configManager.validateConfig();
    if (configValidation.isValid) {
        console.log(`${chalk.yellow('Configuration:')} ${chalk.green('Valid')}`);
    }
    else {
        console.log(`${chalk.yellow('Configuration:')} ${chalk.red('Invalid')}`);
        configValidation.errors.forEach(error => {
            console.log(`  ${chalk.red('•')} ${error}`);
        });
    }
    // Test providers
    console.log(chalk.yellow('\nTesting Providers:'));
    const errorHandler = new ErrorHandler(configManager.getRetryConfig());
    // Test Deepseek
    try {
        const deepseek = new DeepseekProvider(config.apiKey, errorHandler);
        const deepseekAvailable = await deepseek.isAvailable();
        console.log(`  ${chalk.yellow('Deepseek:')} ${deepseekAvailable ? chalk.green('Available') : chalk.red('Unavailable')}`);
    }
    catch (error) {
        console.log(`  ${chalk.yellow('Deepseek:')} ${chalk.red('Error - ' + error.message)}`);
    }
    // Test Ollama
    try {
        const ollama = new OllamaProvider(config.baseUrl || 'http://localhost:11434', errorHandler);
        const ollamaAvailable = await ollama.isAvailable();
        console.log(`  ${chalk.yellow('Ollama:')} ${ollamaAvailable ? chalk.green('Available') : chalk.red('Unavailable')}`);
    }
    catch (error) {
        console.log(`  ${chalk.yellow('Ollama:')} ${chalk.red('Error - ' + error.message)}`);
    }
    console.log(chalk.green('\n✅ Diagnostics complete'));
}
async function checkUpdates() {
    console.log(chalk.blue('🔍 Checking for updates...'));
    console.log(chalk.yellow('Update checking feature will be implemented in a future version.'));
    console.log(chalk.gray(`Current version: ${packageJson.version}`));
}
//# sourceMappingURL=index.js.map