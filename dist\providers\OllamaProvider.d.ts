import { <PERSON><PERSON>rovider, ChatMessage, LLMResponse, LLMStreamChunk, GenerationOptions } from '../types/index.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
export declare class OllamaProvider implements LLMProvider {
    name: string;
    models: string[];
    baseUrl: string;
    private errorHandler;
    constructor(baseUrl?: string, errorHandler?: ErrorHandler);
    isAvailable(): Promise<boolean>;
    loadModels(): Promise<void>;
    generateResponse(messages: ChatMessage[], options?: GenerationOptions): Promise<LLMResponse>;
    streamResponse(messages: ChatMessage[], options?: GenerationOptions): AsyncGenerator<LLMStreamChunk>;
    private formatMessages;
    private formatTools;
    private parseToolCalls;
    private getDefaultModel;
    setBaseUrl(baseUrl: string): void;
    getModels(): Promise<string[]>;
    pullModel(modelName: string): Promise<void>;
    deleteModel(modelName: string): Promise<void>;
    getSystemPrompt(): string;
}
//# sourceMappingURL=OllamaProvider.d.ts.map