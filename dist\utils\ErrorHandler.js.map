{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,OAAO,YAAY;IACf,WAAW,CAAc;IACzB,QAAQ,GAAa,MAAM,CAAC;IAEpC,YAAY,WAAwB;QAClC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,WAAW,CAAC,KAAe;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,SAAS,CACb,SAA2B,EAC3B,OAAqB,EACrB,iBAAwC;QAExC,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,iBAAiB,EAAE,CAAC;QAC7D,IAAI,SAAgB,CAAC;QAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC9D,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;gBACjC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;oBAChB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,6BAA6B,OAAO,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC5E,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAC3B,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC;gBAE7B,IAAI,OAAO,KAAK,MAAM,CAAC,UAAU,EAAE,CAAC;oBAClC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,MAAM,CAAC,UAAU,UAAU,EAAE;wBACvE,GAAG,OAAO;wBACV,KAAK,EAAE,SAAS,CAAC,OAAO;qBACzB,CAAC,CAAC;oBACH,MAAM;gBACR,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,iCAAiC,EAAE;wBACnD,GAAG,OAAO;wBACV,KAAK,EAAE,SAAS,CAAC,OAAO;qBACzB,CAAC,CAAC;oBACH,MAAM;gBACR,CAAC;gBAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACnD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,OAAO,GAAG,CAAC,wBAAwB,KAAK,IAAI,EAAE;oBACxE,GAAG,OAAO;oBACV,KAAK,EAAE,SAAS,CAAC,OAAO;iBACzB,CAAC,CAAC;gBAEH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,SAAU,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAY,EAAE,MAAmB;QACxD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,SAAS,GAAI,KAAa,CAAC,IAAI,CAAC;QAEtC,iCAAiC;QACjC,IAAI,SAAS,IAAI,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAClD,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CACpD,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,MAAmB;QACzD,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACxF,MAAM,aAAa,GAAG,gBAAgB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa;QACnF,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,WAAW,CAAC,KAAY,EAAE,OAAqB;QAC7C,MAAM,SAAS,GAAG;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAE/C,sCAAsC;QACtC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAEO,gBAAgB,CAAC,KAAY,EAAE,OAAqB;QAC1D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAChD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAEhD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEO,kBAAkB,CAAC,KAAY,EAAE,OAAqB;QAC5D,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAEjD,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9E,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,mDAAmD,CAAC,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC,CAAC;QAC/E,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,sCAAsC,CAAC,CAAC,CAAC;YACpE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC,CAAC;QAC9F,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACnF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,iDAAiD,CAAC,CAAC,CAAC;YAC/E,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,kDAAkD,CAAC,CAAC,CAAC;YAChF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,mDAAmD,CAAC,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEO,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,OAAa;QACzD,MAAM,MAAM,GAA6B;YACvC,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;SACT,CAAC;QAEF,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAElD,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,YAAY,IAAI,OAAO,EAAE,CAAC,CAAC;QAErE,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAe;QACtC,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,KAAK,MAAM;gBACT,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/B,KAAK,MAAM;gBACT,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9B;gBACE,OAAO,IAAK,KAAgB,CAAC,WAAW,EAAE,GAAG,CAAC;QAClD,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,kBAAkB,CAAC,KAAY,EAAE,OAAqB;QACpD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,kBAAkB,CAAC,KAAY,EAAE,OAAe,EAAE,UAAiC,EAAE;QACnF,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YACtB,GAAG,OAAO;YACV,SAAS,EAAE,mBAAmB;YAC9B,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE,CAAC;SACd,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,KAAY,EAAE,QAAgB,EAAE,KAAa,EAAE,UAAiC,EAAE;QACpG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YACtB,GAAG,OAAO;YACV,SAAS,EAAE,aAAa;YACxB,QAAQ;YACR,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE,CAAC;SACd,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB;IACrB,cAAc,CAAC,MAA0B,EAAE,QAAgB;QACzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,gEAAgE,CAAC,CAAC;QACvH,CAAC;IACH,CAAC;IAED,aAAa,CAAC,KAAa,EAAE,eAAyB;QACpD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,UAAU,KAAK,yCAAyC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAED,eAAe,CAAC,OAAe;QAC7B,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,2CAA2C;QAC3C,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACvF,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,4CAA4C,SAAS,+BAA+B,CAAC,CAAC;YACxG,CAAC;QACH,CAAC;IACH,CAAC;CACF"}