import ora from 'ora';
import chalk from 'chalk';
export class ThinkingSpinner {
    spinner = null;
    isActive = false;
    currentMessage = '';
    spinnerFrames = [
        '🤔 Thinking',
        '🧠 Processing',
        '⚡ Analyzing',
        '🔍 Examining',
        '💭 Considering',
        '🎯 Focusing',
        '🚀 Computing',
        '✨ Generating'
    ];
    dotFrames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    constructor() {
        // Initialize with custom spinner
        this.setupCustomSpinner();
    }
    setupCustomSpinner() {
        // Create a custom spinner with our frames
        const customSpinner = {
            interval: 150,
            frames: this.dotFrames
        };
        this.spinner = ora({
            spinner: customSpinner,
            color: 'cyan',
            hideCursor: true
        });
    }
    start(message = 'AI is thinking...') {
        if (this.isActive) {
            this.stop();
        }
        this.currentMessage = message;
        this.isActive = true;
        if (this.spinner) {
            this.spinner.text = chalk.cyan(message);
            this.spinner.start();
            // Add dynamic message rotation
            this.startMessageRotation();
        }
    }
    updateMessage(message) {
        this.currentMessage = message;
        if (this.spinner && this.isActive) {
            this.spinner.text = chalk.cyan(message);
        }
    }
    stop() {
        if (this.spinner && this.isActive) {
            this.spinner.stop();
            this.isActive = false;
        }
    }
    succeed(message) {
        if (this.spinner && this.isActive) {
            this.spinner.succeed(chalk.green(message || 'Complete!'));
            this.isActive = false;
        }
    }
    fail(message) {
        if (this.spinner && this.isActive) {
            this.spinner.fail(chalk.red(message || 'Failed!'));
            this.isActive = false;
        }
    }
    warn(message) {
        if (this.spinner && this.isActive) {
            this.spinner.warn(chalk.yellow(message || 'Warning!'));
            this.isActive = false;
        }
    }
    info(message) {
        if (this.spinner && this.isActive) {
            this.spinner.info(chalk.blue(message || 'Info'));
            this.isActive = false;
        }
    }
    startMessageRotation() {
        if (!this.isActive)
            return;
        let frameIndex = 0;
        const rotationInterval = setInterval(() => {
            if (!this.isActive) {
                clearInterval(rotationInterval);
                return;
            }
            const frame = this.spinnerFrames[frameIndex % this.spinnerFrames.length];
            const dots = '...'.substring(0, (frameIndex % 4));
            if (this.spinner) {
                this.spinner.text = chalk.cyan(`${frame}${dots}`);
            }
            frameIndex++;
        }, 800);
    }
    isSpinning() {
        return this.isActive;
    }
    // Preset spinner configurations for different operations
    startThinking() {
        this.start('🤔 AI is thinking...');
    }
    startExecuting() {
        this.start('⚡ Executing command...');
    }
    startProcessing() {
        this.start('🔄 Processing output...');
    }
    startConnecting() {
        this.start('🌐 Connecting to AI provider...');
    }
    startAnalyzing() {
        this.start('🔍 Analyzing results...');
    }
    startGenerating() {
        this.start('✨ Generating response...');
    }
    // Advanced spinner with progress indication
    startWithProgress(message, totalSteps) {
        return new ProgressSpinner(message, totalSteps);
    }
}
export class ProgressSpinner {
    spinner;
    currentStep = 0;
    totalSteps;
    baseMessage;
    constructor(message, totalSteps) {
        this.baseMessage = message;
        this.totalSteps = totalSteps;
        this.spinner = ora({
            text: this.getProgressText(),
            color: 'cyan'
        }).start();
    }
    getProgressText() {
        const percentage = Math.round((this.currentStep / this.totalSteps) * 100);
        const progressBar = this.createProgressBar(percentage);
        return `${this.baseMessage} ${progressBar} ${percentage}% (${this.currentStep}/${this.totalSteps})`;
    }
    createProgressBar(percentage) {
        const width = 20;
        const filled = Math.round((percentage / 100) * width);
        const empty = width - filled;
        return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    }
    updateProgress(step, message) {
        this.currentStep = Math.min(step, this.totalSteps);
        if (message) {
            this.baseMessage = message;
        }
        this.spinner.text = this.getProgressText();
    }
    nextStep(message) {
        this.updateProgress(this.currentStep + 1, message);
    }
    complete(message) {
        this.currentStep = this.totalSteps;
        this.spinner.succeed(chalk.green(message || `${this.baseMessage} - Complete!`));
    }
    fail(message) {
        this.spinner.fail(chalk.red(message || `${this.baseMessage} - Failed!`));
    }
    stop() {
        this.spinner.stop();
    }
}
// Utility function to create themed spinners
export function createThemedSpinner(theme = 'dark') {
    const spinner = new ThinkingSpinner();
    // Customize based on theme
    if (theme === 'light') {
        // Light theme customizations could go here
    }
    return spinner;
}
// Utility function for command execution spinner
export function createCommandSpinner(command) {
    const spinner = new ThinkingSpinner();
    spinner.start(`⚡ Executing: ${chalk.yellow(command)}`);
    return spinner;
}
// Utility function for AI response spinner
export function createAISpinner(provider, model) {
    const spinner = new ThinkingSpinner();
    spinner.start(`🤖 ${provider}/${model} is generating response...`);
    return spinner;
}
//# sourceMappingURL=ThinkingSpinner.js.map