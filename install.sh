#!/bin/bash

# Arien AI CLI - Universal Installation Script
# Supports: Linux, macOS, Windows WSL
# Features: Global installation, automatic PATH setup, dependency checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${CYAN}[$1] $2${NC}"
}

# Banner
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "╔════════════════════════════════════════════════════════════════════╗"
    echo "║                    🤖 Arien AI CLI Universal Installer             ║"
    echo "║                 Intelligent CLI Assistant with LLM                 ║"
    echo "║                                                                    ║"
    echo "║  🌍 Cross-Platform: Linux, macOS, Windows WSL                     ║"
    echo "║  🧠 AI Providers: Deepseek, Ollama                                ║"
    echo "║  🔧 Features: Shell Commands, Real-time Streaming, Error Handling ║"
    echo "╚════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# Check system requirements
check_requirements() {
    log_step "1" "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 20+ first."
        log_info "Visit: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 20 ]; then
        log_error "Node.js 20+ required. Current version: $(node --version)"
        log_info "Please update Node.js: https://nodejs.org/"
        exit 1
    fi
    log_success "Node.js $(node --version) ✓"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    log_success "npm $(npm --version) ✓"
    
    # Check git
    if ! command -v git &> /dev/null; then
        log_warning "git is not installed (recommended)"
    else
        log_success "git $(git --version | cut -d' ' -f3) ✓"
    fi
    
    echo
}

# Install function
install_arien() {
    log_step "2" "Installing Arien AI CLI..."
    
    # Check if we're in the project directory
    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Install dependencies
    log_info "Installing dependencies..."
    npm install
    
    # Build the project
    log_info "Building project..."
    npm run build
    
    # Install globally
    log_info "Installing globally..."
    if npm install -g . 2>/dev/null; then
        log_success "Global installation completed!"
    else
        log_warning "Permission denied. Trying with sudo..."
        if sudo npm install -g .; then
            log_success "Global installation completed with sudo!"
        else
            log_error "Global installation failed"
            exit 1
        fi
    fi
    
    echo
}

# Verify installation
verify_installation() {
    log_step "3" "Verifying installation..."
    
    if command -v arien &> /dev/null; then
        VERSION=$(arien --version 2>/dev/null || echo "unknown")
        log_success "Arien AI CLI installed successfully! Version: $VERSION"
    else
        log_warning "arien command not found in PATH"
        log_info "You may need to restart your terminal or add npm global bin to PATH"
        
        # Show PATH instructions
        echo
        log_info "To add npm global bin to PATH, run:"
        echo "  echo 'export PATH=\"\$(npm config get prefix)/bin:\$PATH\"' >> ~/.bashrc"
        echo "  source ~/.bashrc"
    fi
    
    echo
}

# Show post-install instructions
show_post_install() {
    echo -e "${GREEN}🎉 Installation Complete!${NC}"
    echo
    echo -e "${CYAN}Quick Start:${NC}"
    echo "  arien                    # Start interactive mode"
    echo "  arien config show        # Show configuration"
    echo "  arien provider list      # List available providers"
    echo "  arien --help             # Show all commands"
    echo
    echo -e "${CYAN}First Time Setup:${NC}"
    echo "  1. Set your API key: arien config set-api-key <your-key>"
    echo "  2. Choose provider: arien config set-provider deepseek"
    echo "  3. Start chatting: arien"
    echo
    echo -e "${BLUE}Documentation: https://github.com/arien-ai/arien-ai-cli${NC}"
    echo
}

# Uninstall function
uninstall_arien() {
    log_step "UNINSTALL" "Removing Arien AI CLI..."
    
    if npm uninstall -g arien-ai-cli 2>/dev/null; then
        log_success "Arien AI CLI uninstalled successfully!"
    else
        log_warning "Trying with sudo..."
        if sudo npm uninstall -g arien-ai-cli; then
            log_success "Arien AI CLI uninstalled successfully!"
        else
            log_error "Failed to uninstall"
            exit 1
        fi
    fi
    
    echo
    read -p "Remove configuration files? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf ~/.arien-ai
        log_success "Configuration files removed"
    fi
}

# Update function
update_arien() {
    log_step "UPDATE" "Updating Arien AI CLI..."
    
    # Uninstall old version
    npm uninstall -g arien-ai-cli 2>/dev/null || sudo npm uninstall -g arien-ai-cli 2>/dev/null || true
    
    # Install new version
    install_arien
    verify_installation
}

# Main menu
main_menu() {
    echo "Select an option:"
    echo "1. 🌍 Install globally (recommended)"
    echo "2. 🔄 Update existing installation"
    echo "3. 🗑️  Uninstall"
    echo "4. 🔧 System diagnostics"
    echo "5. ❌ Cancel"
    echo
    
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            install_arien
            verify_installation
            show_post_install
            ;;
        2)
            update_arien
            show_post_install
            ;;
        3)
            uninstall_arien
            ;;
        4)
            check_requirements
            if command -v arien &> /dev/null; then
                arien doctor
            else
                log_info "Arien AI CLI is not installed"
            fi
            ;;
        5)
            log_info "Installation cancelled"
            exit 0
            ;;
        *)
            log_error "Invalid choice"
            exit 1
            ;;
    esac
}

# Main execution
main() {
    show_banner
    check_requirements
    main_menu
}

# Run main function
main "$@"
