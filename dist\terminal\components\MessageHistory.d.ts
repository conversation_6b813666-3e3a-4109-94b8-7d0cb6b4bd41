import { ChatMessage } from '../../types/index.js';
import { ConfigManager } from '../../config/ConfigManager.js';
export interface HistoryEntry {
    id: string;
    timestamp: Date;
    sessionId?: string;
    message: ChatMessage;
    response?: ChatMessage;
    executionTime?: number;
}
export declare class MessageHistory {
    private history;
    private currentIndex;
    private configManager;
    private maxHistorySize;
    constructor(configManager: ConfigManager);
    addEntry(message: ChatMessage, response?: ChatMessage, sessionId?: string): string;
    getEntry(id: string): HistoryEntry | undefined;
    getAllEntries(): HistoryEntry[];
    getEntriesForSession(sessionId: string): HistoryEntry[];
    getRecentEntries(count: number): HistoryEntry[];
    searchEntries(query: string): HistoryEntry[];
    navigateHistory(direction: 'up' | 'down'): string | null;
    getCurrentHistoryIndex(): number;
    resetHistoryNavigation(): void;
    clearHistory(): void;
    clearSessionHistory(sessionId: string): void;
    exportHistory(format?: 'json' | 'text'): string;
    importHistory(data: string, format?: 'json' | 'text'): void;
    getStatistics(): {
        totalEntries: number;
        sessionsCount: number;
        averageResponseTime: number;
        mostActiveSession: string | null;
        oldestEntry: Date | null;
        newestEntry: Date | null;
    };
    formatHistoryForDisplay(entries?: HistoryEntry[]): string;
    private generateId;
    private loadHistory;
    private saveHistory;
    setMaxHistorySize(size: number): void;
    getMaxHistorySize(): number;
}
//# sourceMappingURL=MessageHistory.d.ts.map