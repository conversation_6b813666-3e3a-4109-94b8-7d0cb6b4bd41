{"version": 3, "file": "ThinkingSpinner.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/ThinkingSpinner.ts"], "names": [], "mappings": "AAAA,OAAO,GAAY,MAAM,KAAK,CAAC;AAC/B,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,OAAO,eAAe;IAClB,OAAO,GAAe,IAAI,CAAC;IAC3B,QAAQ,GAAG,KAAK,CAAC;IACjB,cAAc,GAAG,EAAE,CAAC;IAEX,aAAa,GAAG;QAC/B,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,cAAc;KACf,CAAC;IAEe,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAEhF;QACE,iCAAiC;QACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,0CAA0C;QAC1C,MAAM,aAAa,GAAG;YACpB,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,SAAS;SACvB,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;YACjB,OAAO,EAAE,aAAa;YACtB,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,OAAO,GAAG,mBAAmB;QACxC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAErB,+BAA+B;YAC/B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAEM,aAAa,CAAC,OAAe;QAClC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEM,IAAI;QACT,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEM,OAAO,CAAC,OAAgB;QAC7B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,aAAa,CAAC,gBAAgB,CAAC,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAElD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC;YACpD,CAAC;YAED,UAAU,EAAE,CAAC;QACf,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,yDAAyD;IAClD,aAAa;QAClB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IACrC,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IACvC,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACxC,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAChD,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACxC,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACzC,CAAC;IAED,4CAA4C;IACrC,iBAAiB,CAAC,OAAe,EAAE,UAAkB;QAC1D,OAAO,IAAI,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IAClB,OAAO,CAAM;IACb,WAAW,GAAG,CAAC,CAAC;IAChB,UAAU,CAAS;IACnB,WAAW,CAAS;IAE5B,YAAY,OAAe,EAAE,UAAkB;QAC7C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;YACjB,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE;YAC5B,KAAK,EAAE,MAAM;SACd,CAAC,CAAC,KAAK,EAAE,CAAC;IACb,CAAC;IAEO,eAAe;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;QAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACvD,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,WAAW,IAAI,UAAU,MAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC;IACtG,CAAC;IAEO,iBAAiB,CAAC,UAAkB;QAC1C,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;QAE7B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC;IAEM,cAAc,CAAC,IAAY,EAAE,OAAgB;QAClD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC7C,CAAC;IAEM,QAAQ,CAAC,OAAgB;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAEM,QAAQ,CAAC,OAAgB;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,WAAW,cAAc,CAAC,CAAC,CAAC;IAClF,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,WAAW,YAAY,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;CACF;AAED,6CAA6C;AAC7C,MAAM,UAAU,mBAAmB,CAAC,QAA0B,MAAM;IAClE,MAAM,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;IAEtC,2BAA2B;IAC3B,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,2CAA2C;IAC7C,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,iDAAiD;AACjD,MAAM,UAAU,oBAAoB,CAAC,OAAe;IAClD,MAAM,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;IACtC,OAAO,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACvD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,2CAA2C;AAC3C,MAAM,UAAU,eAAe,CAAC,QAAgB,EAAE,KAAa;IAC7D,MAAM,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;IACtC,OAAO,CAAC,KAAK,CAAC,MAAM,QAAQ,IAAI,KAAK,4BAA4B,CAAC,CAAC;IACnE,OAAO,OAAO,CAAC;AACjB,CAAC"}