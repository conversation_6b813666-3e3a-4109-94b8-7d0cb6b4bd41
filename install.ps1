# Arien AI CLI - Windows PowerShell Installation Script
# Supports: Windows 10/11, Windows WSL
# Features: Global installation, automatic PATH setup, dependency checks

param(
    [string]$Action = "install"
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    White = "White"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Write-Step {
    param(
        [string]$Step,
        [string]$Message
    )
    Write-ColorOutput "[$Step] $Message" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Blue"
}

function Show-Banner {
    Clear-Host
    Write-ColorOutput "╔════════════════════════════════════════════════════════════════════╗" "Cyan"
    Write-ColorOutput "║                    🤖 Arien AI CLI Windows Installer               ║" "Cyan"
    Write-ColorOutput "║                 Intelligent CLI Assistant with LLM                 ║" "Cyan"
    Write-ColorOutput "║                                                                    ║" "Cyan"
    Write-ColorOutput "║  🪟 Platform: Windows 10/11, WSL                                  ║" "White"
    Write-ColorOutput "║  🧠 AI Providers: Deepseek, Ollama                                ║" "White"
    Write-ColorOutput "║  🔧 Features: Shell Commands, Real-time Streaming, Error Handling ║" "White"
    Write-ColorOutput "╚════════════════════════════════════════════════════════════════════╝" "Cyan"
    Write-Host ""
}

function Test-Requirements {
    Write-Step "1" "Checking system requirements..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
        
        if ($versionNumber -lt 20) {
            Write-Error "Node.js 20+ required. Current version: $nodeVersion"
            Write-Info "Please update Node.js: https://nodejs.org/"
            exit 1
        }
        Write-Success "Node.js $nodeVersion ✓"
    }
    catch {
        Write-Error "Node.js is not installed. Please install Node.js 20+ first."
        Write-Info "Visit: https://nodejs.org/"
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-Success "npm $npmVersion ✓"
    }
    catch {
        Write-Error "npm is not installed"
        exit 1
    }
    
    # Check git
    try {
        $gitVersion = git --version
        Write-Success "$gitVersion ✓"
    }
    catch {
        Write-Warning "git is not installed (recommended)"
    }
    
    # Check execution policy
    $executionPolicy = Get-ExecutionPolicy
    if ($executionPolicy -eq "Restricted") {
        Write-Warning "PowerShell execution policy is Restricted"
        Write-Info "You may need to run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser"
    }
    
    Write-Host ""
}

function Install-ArienAI {
    Write-Step "2" "Installing Arien AI CLI..."
    
    # Check if we're in the project directory
    if (-not (Test-Path "package.json")) {
        Write-Error "package.json not found. Please run this script from the project root directory."
        exit 1
    }
    
    try {
        # Install dependencies
        Write-Info "Installing dependencies..."
        npm install
        
        # Build the project
        Write-Info "Building project..."
        npm run build
        
        # Install globally
        Write-Info "Installing globally..."
        npm install -g .
        
        Write-Success "Global installation completed!"
    }
    catch {
        Write-Error "Installation failed: $($_.Exception.Message)"
        exit 1
    }
    
    Write-Host ""
}

function Test-Installation {
    Write-Step "3" "Verifying installation..."
    
    try {
        $version = arien --version 2>$null
        Write-Success "Arien AI CLI installed successfully! Version: $version"
    }
    catch {
        Write-Warning "arien command not found in PATH"
        Write-Info "You may need to restart your terminal or add npm global bin to PATH"
        
        # Show PATH instructions
        Write-Host ""
        Write-Info "To add npm global bin to PATH:"
        Write-Host "  1. Get npm prefix: npm config get prefix"
        Write-Host "  2. Add the bin folder to your PATH environment variable"
        Write-Host "  3. Restart PowerShell"
    }
    
    Write-Host ""
}

function Show-PostInstall {
    Write-ColorOutput "🎉 Installation Complete!" "Green"
    Write-Host ""
    Write-ColorOutput "Quick Start:" "Cyan"
    Write-Host "  arien                    # Start interactive mode"
    Write-Host "  arien config show        # Show configuration"
    Write-Host "  arien provider list      # List available providers"
    Write-Host "  arien --help             # Show all commands"
    Write-Host ""
    Write-ColorOutput "First Time Setup:" "Cyan"
    Write-Host "  1. Set your API key: arien config set-api-key <your-key>"
    Write-Host "  2. Choose provider: arien config set-provider deepseek"
    Write-Host "  3. Start chatting: arien"
    Write-Host ""
    Write-ColorOutput "Documentation: https://github.com/arien-ai/arien-ai-cli" "Blue"
    Write-Host ""
}

function Uninstall-ArienAI {
    Write-Step "UNINSTALL" "Removing Arien AI CLI..."
    
    try {
        npm uninstall -g arien-ai-cli
        Write-Success "Arien AI CLI uninstalled successfully!"
    }
    catch {
        Write-Error "Failed to uninstall: $($_.Exception.Message)"
        exit 1
    }
    
    Write-Host ""
    $removeConfig = Read-Host "Remove configuration files? (y/N)"
    if ($removeConfig -eq "y" -or $removeConfig -eq "Y") {
        $configPath = Join-Path $env:USERPROFILE ".arien-ai"
        if (Test-Path $configPath) {
            Remove-Item -Recurse -Force $configPath
            Write-Success "Configuration files removed"
        }
    }
}

function Update-ArienAI {
    Write-Step "UPDATE" "Updating Arien AI CLI..."
    
    # Uninstall old version
    try {
        npm uninstall -g arien-ai-cli 2>$null
    }
    catch {
        # Ignore errors
    }
    
    # Install new version
    Install-ArienAI
    Test-Installation
}

function Show-MainMenu {
    Write-Host "Select an option:"
    Write-Host "1. 🌍 Install globally (recommended)"
    Write-Host "2. 🔄 Update existing installation"
    Write-Host "3. 🗑️  Uninstall"
    Write-Host "4. 🔧 System diagnostics"
    Write-Host "5. ❌ Cancel"
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-5)"
    
    switch ($choice) {
        "1" {
            Install-ArienAI
            Test-Installation
            Show-PostInstall
        }
        "2" {
            Update-ArienAI
            Show-PostInstall
        }
        "3" {
            Uninstall-ArienAI
        }
        "4" {
            Test-Requirements
            try {
                arien doctor
            }
            catch {
                Write-Info "Arien AI CLI is not installed"
            }
        }
        "5" {
            Write-Info "Installation cancelled"
            exit 0
        }
        default {
            Write-Error "Invalid choice"
            exit 1
        }
    }
}

function Main {
    Show-Banner
    Test-Requirements
    Show-MainMenu
}

# Run main function
Main
