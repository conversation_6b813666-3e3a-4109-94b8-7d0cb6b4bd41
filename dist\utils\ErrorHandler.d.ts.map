{"version": 3, "file": "ErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAGxE,qBAAa,YAAY;IACvB,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,QAAQ,CAAoB;gBAExB,WAAW,EAAE,WAAW;IAIpC,WAAW,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IAI5B,SAAS,CAAC,CAAC,EACf,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,EAAE,YAAY,EACrB,iBAAiB,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,GACvC,OAAO,CAAC,CAAC,CAAC;IA4Cb,OAAO,CAAC,gBAAgB;IAexB,OAAO,CAAC,cAAc;IAMtB,OAAO,CAAC,KAAK;IAIb,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI;IActD,OAAO,CAAC,gBAAgB;IAwBxB,OAAO,CAAC,kBAAkB;IAqB1B,OAAO,CAAC,GAAG;IAsBX,OAAO,CAAC,gBAAgB;IAgBxB,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI;IAI7D,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,GAAG,IAAI;IAU5F,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,GAAG,IAAI;IAY7G,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAMlE,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,IAAI;IAM7D,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;CAevC"}